{"version": 3, "sources": ["../../@radix-ui/react-select/src/select.tsx", "../../@radix-ui/react-dismissable-layer/src/dismissable-layer.tsx", "../../@radix-ui/react-use-escape-keydown/src/use-escape-keydown.tsx", "../../@radix-ui/react-focus-guards/src/focus-guards.tsx", "../../@radix-ui/react-focus-scope/src/focus-scope.tsx", "../../@radix-ui/react-popper/src/popper.tsx", "../../@floating-ui/utils/dist/floating-ui.utils.mjs", "../../@floating-ui/core/dist/floating-ui.core.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../@floating-ui/dom/dist/floating-ui.dom.mjs", "../../@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "../../@radix-ui/react-arrow/src/arrow.tsx", "../../@radix-ui/react-portal/src/portal.tsx", "../../@radix-ui/react-visually-hidden/src/visually-hidden.tsx", "../../aria-hidden/dist/es2015/index.js", "../../tslib/tslib.es6.mjs", "../../react-remove-scroll/dist/es2015/Combination.js", "../../react-remove-scroll/dist/es2015/UI.js", "../../react-remove-scroll-bar/dist/es2015/constants.js", "../../use-callback-ref/dist/es2015/assignRef.js", "../../use-callback-ref/dist/es2015/useRef.js", "../../use-callback-ref/dist/es2015/useMergeRef.js", "../../use-sidecar/dist/es2015/hoc.js", "../../use-sidecar/dist/es2015/hook.js", "../../use-sidecar/dist/es2015/medium.js", "../../use-sidecar/dist/es2015/renderProp.js", "../../use-sidecar/dist/es2015/exports.js", "../../react-remove-scroll/dist/es2015/medium.js", "../../react-remove-scroll/dist/es2015/SideEffect.js", "../../react-remove-scroll-bar/dist/es2015/component.js", "../../react-style-singleton/dist/es2015/hook.js", "../../get-nonce/dist/es2015/index.js", "../../react-style-singleton/dist/es2015/singleton.js", "../../react-style-singleton/dist/es2015/component.js", "../../react-remove-scroll-bar/dist/es2015/utils.js", "../../react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../react-remove-scroll/dist/es2015/handleScroll.js", "../../react-remove-scroll/dist/es2015/sidecar.js"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VISUALLY_HIDDEN_STYLES } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value: string | undefined;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface ControlledClearableSelectProps {\n  value: string | undefined;\n  defaultValue?: never;\n  onValueChange: (value: string | undefined) => void;\n}\n\ninterface ControlledUnclearableSelectProps {\n  value: string;\n  defaultValue?: never;\n  onValueChange: (value: string) => void;\n}\n\ninterface UncontrolledSelectProps {\n  value?: never;\n  defaultValue?: string;\n  onValueChange?: {\n    (value: string): void;\n    (value: string | undefined): void;\n  };\n}\n\ntype SelectControlProps =\n  | ControlledClearableSelectProps\n  | ControlledUnclearableSelectProps\n  | UncontrolledSelectProps;\n\ninterface SelectSharedProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\n// TODO: Should improve typing somewhat, but this would be a breaking change.\n// Consider using in the next major version (along with some testing to be sure\n// it works as expected and doesn't cause problems)\ntype _FutureSelectProps = SelectSharedProps & SelectControlProps;\n\ntype SelectProps = SelectSharedProps & {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n};\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange as any,\n    caller: SELECT_NAME,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <SelectBubbleInput\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </SelectBubbleInput>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst Slot = createSlot('SelectContent.RemoveScroll');\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem =\n          items.length > 0 && selectedItem === items[items.length - 1]!.ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0]!.ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SelectBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.select>;\ninterface SwitchBubbleInputProps extends InputProps {}\n\nconst SelectBubbleInput = React.forwardRef<HTMLSelectElement, SwitchBubbleInputProps>(\n  ({ __scopeSelect, value, ...props }: ScopedProps<SwitchBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much as\n     * possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange`\n     * event. Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use visually hidden styles rather than `display: \"none\"` because\n     * Safari autofill won't work otherwise.\n     */\n    return (\n      <Primitive.select\n        {...props}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled!); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ComponentRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      } else {\n        // We need to remove the event listener in case the outside click has been canceled.\n        // See: https://github.com/radix-ui/primitives/issues/2171\n        ownerDocument.removeEventListener('click', handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(\n  onFocusOutside?: (event: FocusOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(\n  onEscapeKeyDownProp?: (event: KeyboardEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\nexport { useEscapeKeydown };\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.outline = 'none';\n  element.style.opacity = '0';\n  element.style.position = 'fixed';\n  element.style.pointerEvents = 'none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = (typeof SIDE_OPTIONS)[number];\ntype Align = (typeof ALIGN_OPTIONS)[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  avoidCollisions?: boolean;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  updatePositionStrategy?: 'optimized' | 'always';\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      updatePositionStrategy = 'optimized',\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === 'always',\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden', ...detectOverflowOptions }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...(middlewareData.hide?.referenceHidden && {\n            visibility: 'hidden',\n            pointerEvents: 'none',\n          }),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ComponentRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = React.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          if (!ignoreCrossAxisOverflow ||\n          // We leave the current main axis only if every placement on that axis\n          // overflows the main axis.\n          overflowsData.every(d => d.overflows[0] > 0 && getSideAxis(d.placement) === initialSideAxis)) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import { computePosition, arrow as arrow$2, autoPlacement as autoPlacement$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar isClient = typeof document !== 'undefined';\n\nvar noop = function noop() {};\nvar index = isClient ? useLayoutEffect : noop;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ComponentRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = React.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: Element | DocumentFragment | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || (mounted && globalThis?.document?.body);\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: 'absolute',\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: 'hidden',\n  clip: 'rect(0, 0, 0, 0)',\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}) satisfies React.CSSProperties;\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n  //\n  VISUALLY_HIDDEN_STYLES,\n};\nexport type { VisuallyHiddenProps };\n", "var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n    // and script elements, as they have no impact on accessibility.\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useSidecar } from './hook';\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function sidecar(importer, errorComponent) {\n    var ErrorCase = function () { return errorComponent; };\n    return function Sidecar(props) {\n        var _a = useSidecar(importer, props.sideCar), Car = _a[0], error = _a[1];\n        if (error && errorComponent) {\n            return ErrorCase;\n        }\n        // @ts-expect-error type shenanigans\n        return Car ? React.createElement(Car, __assign({}, props)) : null;\n    };\n}\n", "import { useState, useEffect } from 'react';\nimport { env } from './env';\nvar cache = new WeakMap();\nvar NO_OPTIONS = {};\nexport function useSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    if (env.isNode && !options.ssr) {\n        return [null, null];\n    }\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return useRealSidecar(importer, effect);\n}\nfunction useRealSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    var couldUseCache = env.forceCache || (env.isNode && !!options.ssr) || !options.async;\n    var _a = useState(couldUseCache ? function () { return cache.get(importer); } : undefined), Car = _a[0], setCar = _a[1];\n    var _b = useState(null), error = _b[0], setError = _b[1];\n    useEffect(function () {\n        if (!Car) {\n            importer().then(function (car) {\n                var resolved = effect ? effect.read() : car.default || car;\n                if (!resolved) {\n                    console.error('Sidecar error: with importer', importer);\n                    var error_1;\n                    if (effect) {\n                        console.error('Sidecar error: with medium', effect);\n                        error_1 = new Error('Sidecar medium was not found');\n                    }\n                    else {\n                        error_1 = new Error('Sidecar was not found in exports');\n                    }\n                    setError(function () { return error_1; });\n                    throw error_1;\n                }\n                cache.set(importer, resolved);\n                setCar(function () { return resolved; });\n            }, function (e) { return setError(function () { return e; }); });\n        }\n    }, []);\n    return [Car, error];\n}\n", "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useState, useCallback, useEffect, useLayoutEffect } from 'react';\nexport function renderCar(WrappedComponent, defaults) {\n    function State(_a) {\n        var stateRef = _a.stateRef, props = _a.props;\n        var renderTarget = useCallback(function SideTarget() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            useLayoutEffect(function () {\n                stateRef.current(args);\n            });\n            return null;\n        }, []);\n        // @ts-ignore\n        return React.createElement(WrappedComponent, __assign({}, props, { children: renderTarget }));\n    }\n    var Children = React.memo(function (_a) {\n        var stateRef = _a.stateRef, defaultState = _a.defaultState, children = _a.children;\n        var _b = useState(defaultState.current), state = _b[0], setState = _b[1];\n        useEffect(function () {\n            stateRef.current = setState;\n        }, []);\n        return children.apply(void 0, state);\n    }, function () { return true; });\n    return function Combiner(props) {\n        var defaultState = React.useRef(defaults(props));\n        var ref = React.useRef(function (state) { return (defaultState.current = state); });\n        return (React.createElement(React.Fragment, null,\n            React.createElement(State, { stateRef: ref, props: props }),\n            React.createElement(Children, { stateRef: ref, defaultState: defaultState, children: props.children })));\n    };\n}\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAuB;AACvB,IAAAC,YAA0B;;;ACD1B,IAAAC,SAAuB;;;ACAvB,YAAuB;AAMvB,SAAS,iBACP,qBACA,gBAA0B,yCAAY,UACtC;AACA,QAAM,kBAAkB,eAAe,mBAAmB;AAEpD,EAAA,gBAAU,MAAM;AACpB,UAAM,gBAAgB,CAAC,UAAyB;AAC9C,UAAI,MAAM,QAAQ,UAAU;AAC1B,wBAAgB,KAAK;MACvB;IACF;AACA,kBAAc,iBAAiB,WAAW,eAAe,EAAE,SAAS,KAAK,CAAC;AAC1E,WAAO,MAAM,cAAc,oBAAoB,WAAW,eAAe,EAAE,SAAS,KAAK,CAAC;EAC5F,GAAG,CAAC,iBAAiB,aAAa,CAAC;AACrC;;;ADqIM,yBAAA;AA/IN,IAAM,yBAAyB;AAC/B,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AAEtB,IAAI;AAEJ,IAAM,0BAAgC,qBAAc;EAClD,QAAQ,oBAAI,IAA6B;EACzC,wCAAwC,oBAAI,IAA6B;EACzE,UAAU,oBAAI,IAAmC;AACnD,CAAC;AAsCD,IAAM,mBAAyB;EAC7B,CAAC,OAAO,iBAAiB;AACvB,UAAM;MACJ,8BAA8B;MAC9B;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,UAAgB,kBAAW,uBAAuB;AACxD,UAAM,CAAC,MAAM,OAAO,IAAU,gBAAyC,IAAI;AAC3E,UAAM,iBAAgB,6BAAM,mBAAiB,yCAAY;AACzD,UAAM,CAAC,EAAE,KAAK,IAAU,gBAAS,CAAC,CAAC;AACnC,UAAM,eAAe,gBAAgB,cAAc,CAACC,UAAS,QAAQA,KAAI,CAAC;AAC1E,UAAM,SAAS,MAAM,KAAK,QAAQ,MAAM;AACxC,UAAM,CAAC,4CAA4C,IAAI,CAAC,GAAG,QAAQ,sCAAsC,EAAE,MAAM,EAAE;AACnH,UAAM,oDAAoD,OAAO,QAAQ,4CAA6C;AACtH,UAAMC,SAAQ,OAAO,OAAO,QAAQ,IAAI,IAAI;AAC5C,UAAM,8BAA8B,QAAQ,uCAAuC,OAAO;AAC1F,UAAM,yBAAyBA,UAAS;AAExC,UAAM,qBAAqB,sBAAsB,CAAC,UAAU;AAC1D,YAAM,SAAS,MAAM;AACrB,YAAM,wBAAwB,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,SAAS,MAAM,CAAC;AAC5F,UAAI,CAAC,0BAA0B,sBAAuB;AACtD,mEAAuB;AACvB,6DAAoB;AACpB,UAAI,CAAC,MAAM,iBAAkB;IAC/B,GAAG,aAAa;AAEhB,UAAM,eAAe,gBAAgB,CAAC,UAAU;AAC9C,YAAM,SAAS,MAAM;AACrB,YAAM,kBAAkB,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,SAAS,MAAM,CAAC;AACtF,UAAI,gBAAiB;AACrB,uDAAiB;AACjB,6DAAoB;AACpB,UAAI,CAAC,MAAM,iBAAkB;IAC/B,GAAG,aAAa;AAEhB,qBAAiB,CAAC,UAAU;AAC1B,YAAM,iBAAiBA,WAAU,QAAQ,OAAO,OAAO;AACvD,UAAI,CAAC,eAAgB;AACrB,yDAAkB;AAClB,UAAI,CAAC,MAAM,oBAAoB,WAAW;AACxC,cAAM,eAAe;AACrB,kBAAU;MACZ;IACF,GAAG,aAAa;AAEV,IAAA,iBAAU,MAAM;AACpB,UAAI,CAAC,KAAM;AACX,UAAI,6BAA6B;AAC/B,YAAI,QAAQ,uCAAuC,SAAS,GAAG;AAC7D,sCAA4B,cAAc,KAAK,MAAM;AACrD,wBAAc,KAAK,MAAM,gBAAgB;QAC3C;AACA,gBAAQ,uCAAuC,IAAI,IAAI;MACzD;AACA,cAAQ,OAAO,IAAI,IAAI;AACvB,qBAAe;AACf,aAAO,MAAM;AACX,YACE,+BACA,QAAQ,uCAAuC,SAAS,GACxD;AACA,wBAAc,KAAK,MAAM,gBAAgB;QAC3C;MACF;IACF,GAAG,CAAC,MAAM,eAAe,6BAA6B,OAAO,CAAC;AAQxD,IAAA,iBAAU,MAAM;AACpB,aAAO,MAAM;AACX,YAAI,CAAC,KAAM;AACX,gBAAQ,OAAO,OAAO,IAAI;AAC1B,gBAAQ,uCAAuC,OAAO,IAAI;AAC1D,uBAAe;MACjB;IACF,GAAG,CAAC,MAAM,OAAO,CAAC;AAEZ,IAAA,iBAAU,MAAM;AACpB,YAAM,eAAe,MAAM,MAAM,CAAC,CAAC;AACnC,eAAS,iBAAiB,gBAAgB,YAAY;AACtD,aAAO,MAAM,SAAS,oBAAoB,gBAAgB,YAAY;IACxE,GAAG,CAAC,CAAC;AAEL,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,OAAO;UACL,eAAe,8BACX,yBACE,SACA,SACF;UACJ,GAAG,MAAM;QACX;QACA,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,cAAc;QACtF,eAAe,qBAAqB,MAAM,eAAe,aAAa,aAAa;QACnF,sBAAsB;UACpB,MAAM;UACN,mBAAmB;QACrB;MAAA;IACF;EAEJ;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,cAAc;AAKpB,IAAM,yBAA+B,kBAGnC,CAAC,OAAO,iBAAiB;AACzB,QAAM,UAAgB,kBAAW,uBAAuB;AACxD,QAAM,MAAY,cAAsC,IAAI;AAC5D,QAAM,eAAe,gBAAgB,cAAc,GAAG;AAEhD,EAAA,iBAAU,MAAM;AACpB,UAAM,OAAO,IAAI;AACjB,QAAI,MAAM;AACR,cAAQ,SAAS,IAAI,IAAI;AACzB,aAAO,MAAM;AACX,gBAAQ,SAAS,OAAO,IAAI;MAC9B;IACF;EACF,GAAG,CAAC,QAAQ,QAAQ,CAAC;AAErB,aAAO,wBAAC,UAAU,KAAV,EAAe,GAAG,OAAO,KAAK,aAAA,CAAc;AACtD,CAAC;AAED,uBAAuB,cAAc;AAYrC,SAAS,sBACP,sBACA,gBAA0B,yCAAY,UACtC;AACA,QAAM,2BAA2B,eAAe,oBAAoB;AACpE,QAAM,8BAAoC,cAAO,KAAK;AACtD,QAAM,iBAAuB,cAAO,MAAM;EAAC,CAAC;AAEtC,EAAA,iBAAU,MAAM;AACpB,UAAM,oBAAoB,CAAC,UAAwB;AACjD,UAAI,MAAM,UAAU,CAAC,4BAA4B,SAAS;AAGxD,YAASC,4CAAT,WAAoD;AAClD;YACE;YACA;YACA;YACA,EAAE,UAAU,KAAK;UACnB;QACF;AAPS,YAAA,2CAAAA;AAFT,cAAM,cAAc,EAAE,eAAe,MAAM;AAuB3C,YAAI,MAAM,gBAAgB,SAAS;AACjC,wBAAc,oBAAoB,SAAS,eAAe,OAAO;AACjE,yBAAe,UAAUA;AACzB,wBAAc,iBAAiB,SAAS,eAAe,SAAS,EAAE,MAAM,KAAK,CAAC;QAChF,OAAO;AACLA,oDAAyC;QAC3C;MACF,OAAO;AAGL,sBAAc,oBAAoB,SAAS,eAAe,OAAO;MACnE;AACA,kCAA4B,UAAU;IACxC;AAcA,UAAM,UAAU,OAAO,WAAW,MAAM;AACtC,oBAAc,iBAAiB,eAAe,iBAAiB;IACjE,GAAG,CAAC;AACJ,WAAO,MAAM;AACX,aAAO,aAAa,OAAO;AAC3B,oBAAc,oBAAoB,eAAe,iBAAiB;AAClE,oBAAc,oBAAoB,SAAS,eAAe,OAAO;IACnE;EACF,GAAG,CAAC,eAAe,wBAAwB,CAAC;AAE5C,SAAO;;IAEL,sBAAsB,MAAO,4BAA4B,UAAU;EACrE;AACF;AAMA,SAAS,gBACP,gBACA,gBAA0B,yCAAY,UACtC;AACA,QAAM,qBAAqB,eAAe,cAAc;AACxD,QAAM,4BAAkC,cAAO,KAAK;AAE9C,EAAA,iBAAU,MAAM;AACpB,UAAM,cAAc,CAAC,UAAsB;AACzC,UAAI,MAAM,UAAU,CAAC,0BAA0B,SAAS;AACtD,cAAM,cAAc,EAAE,eAAe,MAAM;AAC3C,qCAA6B,eAAe,oBAAoB,aAAa;UAC3E,UAAU;QACZ,CAAC;MACH;IACF;AACA,kBAAc,iBAAiB,WAAW,WAAW;AACrD,WAAO,MAAM,cAAc,oBAAoB,WAAW,WAAW;EACvE,GAAG,CAAC,eAAe,kBAAkB,CAAC;AAEtC,SAAO;IACL,gBAAgB,MAAO,0BAA0B,UAAU;IAC3D,eAAe,MAAO,0BAA0B,UAAU;EAC5D;AACF;AAEA,SAAS,iBAAiB;AACxB,QAAM,QAAQ,IAAI,YAAY,cAAc;AAC5C,WAAS,cAAc,KAAK;AAC9B;AAEA,SAAS,6BACP,MACA,SACA,QACA,EAAE,SAAS,GACX;AACA,QAAM,SAAS,OAAO,cAAc;AACpC,QAAM,QAAQ,IAAI,YAAY,MAAM,EAAE,SAAS,OAAO,YAAY,MAAM,OAAO,CAAC;AAChF,MAAI,QAAS,QAAO,iBAAiB,MAAM,SAA0B,EAAE,MAAM,KAAK,CAAC;AAEnF,MAAI,UAAU;AACZ,gCAA4B,QAAQ,KAAK;EAC3C,OAAO;AACL,WAAO,cAAc,KAAK;EAC5B;AACF;;;AE3VA,IAAAC,SAAuB;AAGvB,IAAI,QAAQ;AAWZ,SAAS,iBAAiB;AAClB,EAAA,iBAAU,MAAM;AACpB,UAAM,aAAa,SAAS,iBAAiB,0BAA0B;AACvE,aAAS,KAAK,sBAAsB,cAAc,WAAW,CAAC,KAAK,iBAAiB,CAAC;AACrF,aAAS,KAAK,sBAAsB,aAAa,WAAW,CAAC,KAAK,iBAAiB,CAAC;AACpF;AAEA,WAAO,MAAM;AACX,UAAI,UAAU,GAAG;AACf,iBAAS,iBAAiB,0BAA0B,EAAE,QAAQ,CAAC,SAAS,KAAK,OAAO,CAAC;MACvF;AACA;IACF;EACF,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,mBAAmB;AAC1B,QAAM,UAAU,SAAS,cAAc,MAAM;AAC7C,UAAQ,aAAa,0BAA0B,EAAE;AACjD,UAAQ,WAAW;AACnB,UAAQ,MAAM,UAAU;AACxB,UAAQ,MAAM,UAAU;AACxB,UAAQ,MAAM,WAAW;AACzB,UAAQ,MAAM,gBAAgB;AAC9B,SAAO;AACT;;;ACvCA,IAAAC,SAAuB;AA2MnB,IAAAC,sBAAA;AAtMJ,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB,EAAE,SAAS,OAAO,YAAY,KAAK;AAQzD,IAAM,mBAAmB;AAgCzB,IAAM,aAAmB,kBAA+C,CAAC,OAAO,iBAAiB;AAC/F,QAAM;IACJ,OAAO;IACP,UAAU;IACV,kBAAkB;IAClB,oBAAoB;IACpB,GAAG;EACL,IAAI;AACJ,QAAM,CAAC,WAAW,YAAY,IAAU,gBAA6B,IAAI;AACzE,QAAM,mBAAmB,eAAe,oBAAoB;AAC5D,QAAM,qBAAqB,eAAe,sBAAsB;AAChE,QAAM,wBAA8B,cAA2B,IAAI;AACnE,QAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,aAAa,IAAI,CAAC;AAE/E,QAAM,aAAmB,cAAO;IAC9B,QAAQ;IACR,QAAQ;AACN,WAAK,SAAS;IAChB;IACA,SAAS;AACP,WAAK,SAAS;IAChB;EACF,CAAC,EAAE;AAGG,EAAA,iBAAU,MAAM;AACpB,QAAI,SAAS;AACX,UAASC,iBAAT,SAAuB,OAAmB;AACxC,YAAI,WAAW,UAAU,CAAC,UAAW;AACrC,cAAM,SAAS,MAAM;AACrB,YAAI,UAAU,SAAS,MAAM,GAAG;AAC9B,gCAAsB,UAAU;QAClC,OAAO;AACL,gBAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAK,CAAC;QACvD;MACF,GAESC,kBAAT,SAAwB,OAAmB;AACzC,YAAI,WAAW,UAAU,CAAC,UAAW;AACrC,cAAM,gBAAgB,MAAM;AAY5B,YAAI,kBAAkB,KAAM;AAI5B,YAAI,CAAC,UAAU,SAAS,aAAa,GAAG;AACtC,gBAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAK,CAAC;QACvD;MACF,GAKSC,mBAAT,SAAyB,WAA6B;AACpD,cAAM,iBAAiB,SAAS;AAChC,YAAI,mBAAmB,SAAS,KAAM;AACtC,mBAAW,YAAY,WAAW;AAChC,cAAI,SAAS,aAAa,SAAS,EAAG,OAAM,SAAS;QACvD;MACF;AA1CS,UAAA,gBAAAF,gBAUA,iBAAAC,iBA0BA,kBAAAC;AAQT,eAAS,iBAAiB,WAAWF,cAAa;AAClD,eAAS,iBAAiB,YAAYC,eAAc;AACpD,YAAM,mBAAmB,IAAI,iBAAiBC,gBAAe;AAC7D,UAAI,UAAW,kBAAiB,QAAQ,WAAW,EAAE,WAAW,MAAM,SAAS,KAAK,CAAC;AAErF,aAAO,MAAM;AACX,iBAAS,oBAAoB,WAAWF,cAAa;AACrD,iBAAS,oBAAoB,YAAYC,eAAc;AACvD,yBAAiB,WAAW;MAC9B;IACF;EACF,GAAG,CAAC,SAAS,WAAW,WAAW,MAAM,CAAC;AAEpC,EAAA,iBAAU,MAAM;AACpB,QAAI,WAAW;AACb,uBAAiB,IAAI,UAAU;AAC/B,YAAM,2BAA2B,SAAS;AAC1C,YAAM,sBAAsB,UAAU,SAAS,wBAAwB;AAEvE,UAAI,CAAC,qBAAqB;AACxB,cAAM,aAAa,IAAI,YAAY,oBAAoB,aAAa;AACpE,kBAAU,iBAAiB,oBAAoB,gBAAgB;AAC/D,kBAAU,cAAc,UAAU;AAClC,YAAI,CAAC,WAAW,kBAAkB;AAChC,qBAAW,YAAY,sBAAsB,SAAS,CAAC,GAAG,EAAE,QAAQ,KAAK,CAAC;AAC1E,cAAI,SAAS,kBAAkB,0BAA0B;AACvD,kBAAM,SAAS;UACjB;QACF;MACF;AAEA,aAAO,MAAM;AACX,kBAAU,oBAAoB,oBAAoB,gBAAgB;AAKlE,mBAAW,MAAM;AACf,gBAAM,eAAe,IAAI,YAAY,sBAAsB,aAAa;AACxE,oBAAU,iBAAiB,sBAAsB,kBAAkB;AACnE,oBAAU,cAAc,YAAY;AACpC,cAAI,CAAC,aAAa,kBAAkB;AAClC,kBAAM,4BAA4B,SAAS,MAAM,EAAE,QAAQ,KAAK,CAAC;UACnE;AAEA,oBAAU,oBAAoB,sBAAsB,kBAAkB;AAEtE,2BAAiB,OAAO,UAAU;QACpC,GAAG,CAAC;MACN;IACF;EACF,GAAG,CAAC,WAAW,kBAAkB,oBAAoB,UAAU,CAAC;AAGhE,QAAM,gBAAsB;IAC1B,CAAC,UAA+B;AAC9B,UAAI,CAAC,QAAQ,CAAC,QAAS;AACvB,UAAI,WAAW,OAAQ;AAEvB,YAAM,WAAW,MAAM,QAAQ,SAAS,CAAC,MAAM,UAAU,CAAC,MAAM,WAAW,CAAC,MAAM;AAClF,YAAM,iBAAiB,SAAS;AAEhC,UAAI,YAAY,gBAAgB;AAC9B,cAAME,aAAY,MAAM;AACxB,cAAM,CAAC,OAAO,IAAI,IAAI,iBAAiBA,UAAS;AAChD,cAAM,4BAA4B,SAAS;AAG3C,YAAI,CAAC,2BAA2B;AAC9B,cAAI,mBAAmBA,WAAW,OAAM,eAAe;QACzD,OAAO;AACL,cAAI,CAAC,MAAM,YAAY,mBAAmB,MAAM;AAC9C,kBAAM,eAAe;AACrB,gBAAI,KAAM,OAAM,OAAO,EAAE,QAAQ,KAAK,CAAC;UACzC,WAAW,MAAM,YAAY,mBAAmB,OAAO;AACrD,kBAAM,eAAe;AACrB,gBAAI,KAAM,OAAM,MAAM,EAAE,QAAQ,KAAK,CAAC;UACxC;QACF;MACF;IACF;IACA,CAAC,MAAM,SAAS,WAAW,MAAM;EACnC;AAEA,aACE,yBAAC,UAAU,KAAV,EAAc,UAAU,IAAK,GAAG,YAAY,KAAK,cAAc,WAAW,cAAA,CAAe;AAE9F,CAAC;AAED,WAAW,cAAc;AAUzB,SAAS,WAAW,YAA2B,EAAE,SAAS,MAAM,IAAI,CAAC,GAAG;AACtE,QAAM,2BAA2B,SAAS;AAC1C,aAAW,aAAa,YAAY;AAClC,UAAM,WAAW,EAAE,OAAO,CAAC;AAC3B,QAAI,SAAS,kBAAkB,yBAA0B;EAC3D;AACF;AAKA,SAAS,iBAAiB,WAAwB;AAChD,QAAM,aAAa,sBAAsB,SAAS;AAClD,QAAM,QAAQ,YAAY,YAAY,SAAS;AAC/C,QAAM,OAAO,YAAY,WAAW,QAAQ,GAAG,SAAS;AACxD,SAAO,CAAC,OAAO,IAAI;AACrB;AAYA,SAAS,sBAAsB,WAAwB;AACrD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;IAC3E,YAAY,CAAC,SAAc;AACzB,YAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,UAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AAIrE,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;IACpE;EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AAGtE,SAAO;AACT;AAMA,SAAS,YAAY,UAAyB,WAAwB;AACpE,aAAW,WAAW,UAAU;AAE9B,QAAI,CAAC,SAAS,SAAS,EAAE,MAAM,UAAU,CAAC,EAAG,QAAO;EACtD;AACF;AAEA,SAAS,SAAS,MAAmB,EAAE,KAAK,GAA2B;AACrE,MAAI,iBAAiB,IAAI,EAAE,eAAe,SAAU,QAAO;AAC3D,SAAO,MAAM;AAEX,QAAI,SAAS,UAAa,SAAS,KAAM,QAAO;AAChD,QAAI,iBAAiB,IAAI,EAAE,YAAY,OAAQ,QAAO;AACtD,WAAO,KAAK;EACd;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,SAAmE;AAC5F,SAAO,mBAAmB,oBAAoB,YAAY;AAC5D;AAEA,SAAS,MAAM,SAAkC,EAAE,SAAS,MAAM,IAAI,CAAC,GAAG;AAExE,MAAI,WAAW,QAAQ,OAAO;AAC5B,UAAM,2BAA2B,SAAS;AAE1C,YAAQ,MAAM,EAAE,eAAe,KAAK,CAAC;AAErC,QAAI,YAAY,4BAA4B,kBAAkB,OAAO,KAAK;AACxE,cAAQ,OAAO;EACnB;AACF;AAOA,IAAM,mBAAmB,uBAAuB;AAEhD,SAAS,yBAAyB;AAEhC,MAAI,QAAyB,CAAC;AAE9B,SAAO;IACL,IAAI,YAA2B;AAE7B,YAAM,mBAAmB,MAAM,CAAC;AAChC,UAAI,eAAe,kBAAkB;AACnC,6DAAkB;MACpB;AAEA,cAAQ,YAAY,OAAO,UAAU;AACrC,YAAM,QAAQ,UAAU;IAC1B;IAEA,OAAO,YAA2B;;AAChC,cAAQ,YAAY,OAAO,UAAU;AACrC,kBAAM,CAAC,MAAP,mBAAU;IACZ;EACF;AACF;AAEA,SAAS,YAAe,OAAY,MAAS;AAC3C,QAAM,eAAe,CAAC,GAAG,KAAK;AAC9B,QAAMC,SAAQ,aAAa,QAAQ,IAAI;AACvC,MAAIA,WAAU,IAAI;AAChB,iBAAa,OAAOA,QAAO,CAAC;EAC9B;AACA,SAAO;AACT;AAEA,SAAS,YAAY,OAAsB;AACzC,SAAO,MAAM,OAAO,CAAC,SAAS,KAAK,YAAY,GAAG;AACpD;;;ACtVA,IAAAC,SAAuB;;;ACKvB,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,QAAM;AAAA,EACzB,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAASC,OAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI,IAAK,QAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACF;;;ACpIA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AASA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,MAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,MAAI,oBAAoB;AACxB,MAAI,iBAAiB,CAAC;AACtB,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,CAAC;AACrB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,MAAM,GAAG;AAAA,MACX;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,qBAAiB;AAAA,MACf,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,QACN,GAAG,eAAe,IAAI;AAAA,QACtB,GAAG;AAAA,MACL;AAAA,IACF;AACA,QAAI,SAAS,cAAc,IAAI;AAC7B;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,WAAW;AACnB,8BAAoB,MAAM;AAAA,QAC5B;AACA,YAAI,MAAM,OAAO;AACf,kBAAQ,MAAM,UAAU,OAAO,MAAMA,UAAS,gBAAgB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC,IAAI,MAAM;AAAA,QACb;AACA,SAAC;AAAA,UACC;AAAA,UACA;AAAA,QACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,MAC9D;AACA,UAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAUA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,OAAO,MAAM,SAAS;AAAA,IACtB,QAAQ,MAAM,SAAS;AAAA,EACzB,IAAI,MAAM;AACV,QAAM,eAAe,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,KAAO,OAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAOA,IAAM,QAAQ,cAAY;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA,MAAM,GAAG,OAAO;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,IACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,iBAAiB,SAAS;AACvC,UAAM,SAAS,cAAc,IAAI;AACjC,UAAM,kBAAkB,MAAMA,UAAS,cAAc,OAAO;AAC5D,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,UAAU,UAAU,WAAW;AACrC,UAAM,aAAa,UAAU,iBAAiB;AAC9C,UAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,UAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,UAAM,oBAAoB,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,QAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,QAAI,CAAC,cAAc,CAAE,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,mBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,IACrE;AACA,UAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,UAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,UAAM,QAAQ;AACd,UAAMC,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,UAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,UAAMC,UAASC,OAAM,OAAO,QAAQF,IAAG;AAMvC,UAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,UAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,WAAO;AAAA,MACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,MACvB,MAAM;AAAA,QACJ,CAAC,IAAI,GAAGC;AAAA,QACR,cAAc,SAASA,UAAS;AAAA,QAChC,GAAI,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF;AACF;AA+GA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAE;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAM3B,WAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,eAAO,CAAC;AAAA,MACV;AACA,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,kBAAkB,YAAY,gBAAgB;AACpD,YAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,YAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,YAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,YAAM,+BAA+B,8BAA8B;AACnE,UAAI,CAAC,+BAA+B,8BAA8B;AAChE,2BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,MACvH;AACA,YAAMC,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,UAAI,eAAe;AACjB,kBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,MAC/B;AACA,UAAI,gBAAgB;AAClB,cAAMC,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,kBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,MACvD;AACA,sBAAgB,CAAC,GAAG,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,YAAI,uBAAuB;AAC3B,cAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,cAAM,gBAAgBF,YAAW,SAAS;AAC1C,YAAI,eAAe;AACjB,gBAAM,0BAA0B,mBAAmB,cAAc,oBAAoB,YAAY,aAAa,IAAI;AAClH,cAAI,CAAC;AAAA;AAAA,UAGL,cAAc,MAAM,OAAK,EAAE,UAAU,CAAC,IAAI,KAAK,YAAY,EAAE,SAAS,MAAM,eAAe,GAAG;AAE5F,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAIA,YAAI,kBAAkB,wBAAwB,cAAc,OAAO,OAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,YAAI,CAAC,gBAAgB;AACnB,kBAAQ,kBAAkB;AAAA,YACxB,KAAK,WACH;AACE,kBAAI;AACJ,oBAAMG,cAAa,yBAAyB,cAAc,OAAO,OAAK;AACpE,oBAAI,8BAA8B;AAChC,wBAAM,kBAAkB,YAAY,EAAE,SAAS;AAC/C,yBAAO,oBAAoB;AAAA;AAAA,kBAG3B,oBAAoB;AAAA,gBACtB;AACA,uBAAO;AAAA,cACT,CAAC,EAAE,IAAI,OAAK,CAAC,EAAE,WAAW,EAAE,UAAU,OAAO,CAAAC,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,uBAAuB,CAAC;AACjM,kBAAID,YAAW;AACb,iCAAiBA;AAAA,cACnB;AACA;AAAA,YACF;AAAA,YACF,KAAK;AACH,+BAAiB;AACjB;AAAA,UACJ;AAAA,QACF;AACA,YAAI,cAAc,gBAAgB;AAChC,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAEA,SAAS,eAAe,UAAU,MAAM;AACtC,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK;AAAA,IACzB,OAAO,SAAS,QAAQ,KAAK;AAAA,IAC7B,QAAQ,SAAS,SAAS,KAAK;AAAA,IAC/B,MAAM,SAAS,OAAO,KAAK;AAAA,EAC7B;AACF;AACA,SAAS,sBAAsB,UAAU;AACvC,SAAO,MAAM,KAAK,UAAQ,SAAS,IAAI,KAAK,CAAC;AAC/C;AAMA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,WAAW;AAAA,QACX,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,cAAQ,UAAU;AAAA,QAChB,KAAK,mBACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,gBAAgB;AAAA,UAClB,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,SAAS;AACxD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,wBAAwB;AAAA,cACxB,iBAAiB,sBAAsB,OAAO;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,QACF,KAAK,WACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,aAAa;AAAA,UACf,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,QAAQ;AACvD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,gBAAgB;AAAA,cAChB,SAAS,sBAAsB,OAAO;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,QACF,SACE;AACE,iBAAO,CAAC;AAAA,QACV;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAwIA,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,UAAAE;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU,SAAS,YAAY;AAAA,IAC/B,WAAW,SAAS,aAAa;AAAA,IACjC,eAAe,SAAS;AAAA,EAC1B;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AASA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,UAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,eAAO,CAAC;AAAA,MACV;AACA,aAAO;AAAA,QACL,GAAG,IAAI,WAAW;AAAA,QAClB,GAAG,IAAI,WAAW;AAAA,QAClB,MAAM;AAAA,UACJ,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAM,QAAQ,SAAU,SAAS;AAC/B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,UAAU;AAAA,UACR,IAAI,UAAQ;AACV,gBAAI;AAAA,cACF,GAAAC;AAAA,cACA,GAAAC;AAAA,YACF,IAAI;AACJ,mBAAO;AAAA,cACL,GAAAD;AAAA,cACA,GAAAC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,YAAY,QAAQ,SAAS,CAAC;AAChD,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,UAAI,eAAe;AACjB,cAAM,UAAU,aAAa,MAAM,QAAQ;AAC3C,cAAM,UAAU,aAAa,MAAM,WAAW;AAC9C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,wBAAgBC,OAAMF,MAAK,eAAeC,IAAG;AAAA,MAC/C;AACA,UAAI,gBAAgB;AAClB,cAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,cAAM,UAAU,cAAc,MAAM,WAAW;AAC/C,cAAMD,OAAM,iBAAiB,SAAS,OAAO;AAC7C,cAAMC,OAAM,iBAAiB,SAAS,OAAO;AAC7C,yBAAiBC,OAAMF,MAAK,gBAAgBC,IAAG;AAAA,MACjD;AACA,YAAM,gBAAgB,QAAQ,GAAG;AAAA,QAC/B,GAAG;AAAA,QACH,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf,CAAC;AACD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,UACJ,GAAG,cAAc,IAAI;AAAA,UACrB,GAAG,cAAc,IAAI;AAAA,UACrB,SAAS;AAAA,YACP,CAAC,QAAQ,GAAG;AAAA,YACZ,CAAC,SAAS,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAM,aAAa,SAAU,SAAS;AACpC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL;AAAA,IACA,GAAG,OAAO;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,QAAAE,UAAS;AAAA,QACT,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,MAC9B,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,YAAY,SAAS;AACvC,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,YAAM,YAAY,SAASA,SAAQ,KAAK;AACxC,YAAM,iBAAiB,OAAO,cAAc,WAAW;AAAA,QACrD,UAAU;AAAA,QACV,WAAW;AAAA,MACb,IAAI;AAAA,QACF,UAAU;AAAA,QACV,WAAW;AAAA,QACX,GAAG;AAAA,MACL;AACA,UAAI,eAAe;AACjB,cAAM,MAAM,aAAa,MAAM,WAAW;AAC1C,cAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,SAAS,GAAG,IAAI,eAAe;AAClF,cAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,UAAU,GAAG,IAAI,eAAe;AACnF,YAAI,gBAAgB,UAAU;AAC5B,0BAAgB;AAAA,QAClB,WAAW,gBAAgB,UAAU;AACnC,0BAAgB;AAAA,QAClB;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,YAAI,uBAAuB;AAC3B,cAAM,MAAM,aAAa,MAAM,UAAU;AACzC,cAAM,eAAe,CAAC,OAAO,MAAM,EAAE,SAAS,QAAQ,SAAS,CAAC;AAChE,cAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,SAAS,GAAG,KAAK,iBAAiB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,SAAS,MAAM,IAAI,MAAM,eAAe,IAAI,eAAe;AACzO,cAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,UAAU,GAAG,KAAK,eAAe,MAAM,yBAAyB,eAAe,WAAW,OAAO,SAAS,uBAAuB,SAAS,MAAM,MAAM,eAAe,eAAe,YAAY;AACpP,YAAI,iBAAiB,UAAU;AAC7B,2BAAiB;AAAA,QACnB,WAAW,iBAAiB,UAAU;AACpC,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,aAAO;AAAA,QACL,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,UAAAN;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,QAAQ,MAAM;AAAA,QAAC;AAAA,QACf,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,UAAU,YAAY,SAAS,MAAM;AAC3C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM;AACV,UAAI;AACJ,UAAI;AACJ,UAAI,SAAS,SAAS,SAAS,UAAU;AACvC,qBAAa;AACb,oBAAY,eAAgB,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ,KAAM,UAAU,SAAS,SAAS;AAAA,MACzI,OAAO;AACL,oBAAY;AACZ,qBAAa,cAAc,QAAQ,QAAQ;AAAA,MAC7C;AACA,YAAM,wBAAwB,SAAS,SAAS,MAAM,SAAS;AAC/D,YAAM,uBAAuB,QAAQ,SAAS,OAAO,SAAS;AAC9D,YAAM,0BAA0B,IAAI,SAAS,SAAS,UAAU,GAAG,qBAAqB;AACxF,YAAM,yBAAyB,IAAI,QAAQ,SAAS,SAAS,GAAG,oBAAoB;AACpF,YAAM,UAAU,CAAC,MAAM,eAAe;AACtC,UAAI,kBAAkB;AACtB,UAAI,iBAAiB;AACrB,WAAK,wBAAwB,MAAM,eAAe,UAAU,QAAQ,sBAAsB,QAAQ,GAAG;AACnG,yBAAiB;AAAA,MACnB;AACA,WAAK,yBAAyB,MAAM,eAAe,UAAU,QAAQ,uBAAuB,QAAQ,GAAG;AACrG,0BAAkB;AAAA,MACpB;AACA,UAAI,WAAW,CAAC,WAAW;AACzB,cAAM,OAAO,IAAI,SAAS,MAAM,CAAC;AACjC,cAAM,OAAO,IAAI,SAAS,OAAO,CAAC;AAClC,cAAM,OAAO,IAAI,SAAS,KAAK,CAAC;AAChC,cAAM,OAAO,IAAI,SAAS,QAAQ,CAAC;AACnC,YAAI,SAAS;AACX,2BAAiB,QAAQ,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,SAAS,KAAK;AAAA,QAC1G,OAAO;AACL,4BAAkB,SAAS,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,KAAK,SAAS,MAAM;AAAA,QAC5G;AAAA,MACF;AACA,YAAM,MAAM;AAAA,QACV,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,iBAAiB,MAAMA,UAAS,cAAc,SAAS,QAAQ;AACrE,UAAI,UAAU,eAAe,SAAS,WAAW,eAAe,QAAQ;AACtE,eAAO;AAAA,UACL,OAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;;;ACphCA,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,UAAU,KAAK,OAAO,eAAe,aAAa;AACrD,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIO,kBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAAS,YAAY,OAAO,CAAC;AAC5D;AACA,SAAS,WAAW,SAAS;AAC3B,SAAO,CAAC,iBAAiB,QAAQ,EAAE,KAAK,cAAY;AAClD,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,cAAc;AACvC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,UAAU,YAAY,IAAIA,kBAAiB,YAAY,IAAI;AAIvE,SAAO,CAAC,aAAa,aAAa,SAAS,UAAU,aAAa,EAAE,KAAK,WAAS,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,aAAa,SAAS,UAAU,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACniB;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,WAAW,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,SAAU,QAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAASA,kBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,UAAM,eAAe,gBAAgB,GAAG;AACxC,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,gBAAgB,kBAAkB,qBAAqB,YAAY,IAAI,CAAC,CAAC;AAAA,EAC9L;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,UAAU,OAAO,eAAe,IAAI,MAAM,IAAI,IAAI,eAAe;AAC9E;;;AClJA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAMC,kBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAY,cAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAAC,UAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AAEA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAI,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAI,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAyB,aAAa,CAAC;AAC7C,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyB,UAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAI,UAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAI,KAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAI,KAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAM,UAAU,UAAU;AAChC,UAAM,YAAY,gBAAgB,UAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACtF,QAAI,aAAa;AACjB,QAAI,gBAAgB,gBAAgB,UAAU;AAC9C,WAAO,iBAAiB,gBAAgB,cAAc,YAAY;AAChE,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAMA,kBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,WAAK;AACL,WAAK;AACL,mBAAa,UAAU,aAAa;AACpC,sBAAgB,gBAAgB,UAAU;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,oBAAoB,SAAS,MAAM;AAC1C,QAAM,aAAa,cAAc,OAAO,EAAE;AAC1C,MAAI,CAAC,MAAM;AACT,WAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO;AAAA,EACnE;AACA,SAAO,KAAK,OAAO;AACrB;AAEA,SAAS,cAAc,iBAAiB,QAAQ,kBAAkB;AAChE,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AACA,QAAM,WAAW,gBAAgB,sBAAsB;AACvD,QAAM,IAAI,SAAS,OAAO,OAAO,cAAc,mBAAmB;AAAA;AAAA,IAElE,oBAAoB,iBAAiB,QAAQ;AAAA;AAC7C,QAAM,IAAI,SAAS,MAAM,OAAO;AAChC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa;AAC7B,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,WAAW,WAAW,WAAW,SAAS,QAAQ,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,YAAY,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,QAAM,0BAA0B,cAAc,YAAY;AAC1D,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,cAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,IAAI,IAAI,aAAa,CAAC;AAC1I,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,IAC3E,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,EAC5E;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAI,IAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAM,IAAI,CAAC,OAAO;AAClB,MAAIA,kBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,SAAK,IAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQ,cAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAM,IAAI,OAAO,MAAM;AACvB,QAAM,IAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgB,mBAAmB,OAAO,CAAC;AAAA,EACpD,WAAW,UAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO;AAAA,MACL,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,OAAO,iBAAiB;AAAA,MACxB,QAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAAC,UAAU,UAAU,KAAK,sBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAOA,kBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAM,UAAU,EAAE,KAAK,YAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiBA,kBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiB,cAAc,OAAO,IAAI;AAG5D,SAAO,UAAU,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgBA,kBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,WAAW,OAAO,IAAI,CAAC,IAAI,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACjK,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0B,cAAc,YAAY;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAI9B,WAAS,4BAA4B;AACnC,YAAQ,IAAI,oBAAoB,eAAe;AAAA,EACjD;AACA,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAC1B,gCAA0B;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,WAAW,CAAC,2BAA2B,iBAAiB;AAC1D,8BAA0B;AAAA,EAC5B;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,MAAM,IAAI,aAAa,CAAC;AACpI,QAAM,IAAI,KAAK,OAAO,OAAO,aAAa,QAAQ,IAAI,WAAW;AACjE,QAAM,IAAI,KAAK,MAAM,OAAO,YAAY,QAAQ,IAAI,WAAW;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,mBAAmB,SAAS;AACnC,SAAOA,kBAAiB,OAAO,EAAE,aAAa;AAChD;AAEA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAAC,cAAc,OAAO,KAAKA,kBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,MAAI,kBAAkB,QAAQ;AAM9B,MAAI,mBAAmB,OAAO,MAAM,iBAAiB;AACnD,sBAAkB,gBAAgB,cAAc;AAAA,EAClD;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,QAAI,kBAAkB,cAAc,OAAO;AAC3C,WAAO,mBAAmB,CAAC,sBAAsB,eAAe,GAAG;AACjE,UAAI,UAAU,eAAe,KAAK,CAAC,mBAAmB,eAAe,GAAG;AACtE,eAAO;AAAA,MACT;AACA,wBAAkB,cAAc,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAK,mBAAmB,YAAY,GAAG;AACvF,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,gBAAgB,sBAAsB,YAAY,KAAK,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,YAAY,GAAG;AAC/H,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAK;AACxD;AAEA,IAAM,kBAAkB,eAAgB,MAAM;AAC5C,QAAM,oBAAoB,KAAK,mBAAmB;AAClD,QAAM,kBAAkB,KAAK;AAC7B,QAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;AAC9D,SAAO;AAAA,IACL,WAAW,8BAA8B,KAAK,WAAW,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,IAC9G,UAAU;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,mBAAmB;AAAA,MAC1B,QAAQ,mBAAmB;AAAA,IAC7B;AAAA,EACF;AACF;AAEA,SAAS,MAAM,SAAS;AACtB,SAAOA,kBAAiB,OAAO,EAAE,cAAc;AACjD;AAEA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE;AAC7E;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAI;AACJ,QAAM,OAAO,mBAAmB,OAAO;AACvC,WAAS,UAAU;AACjB,QAAI;AACJ,iBAAa,SAAS;AACtB,KAAC,MAAM,OAAO,QAAQ,IAAI,WAAW;AACrC,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,YAAQ;AACR,UAAM,2BAA2B,QAAQ,sBAAsB;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAW,MAAM,GAAG;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAc,MAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAY,MAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAW,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AAGV,sBAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAI;AAAA,QACT,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,UAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,sBAAsB,CAAC,GAAG;AAQ5F,gBAAQ;AAAA,MACV;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe;AAAA,QAC3C,GAAG;AAAA;AAAA,QAEH,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH,SAAS,IAAI;AACX,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAO;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,cAAI;AACJ,WAAC,kBAAkB,mBAAmB,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA,QAChF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,eAAe,CAAC,cAAc,aAAa,WAAW,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,QAAI;AACJ,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,QAAQ,UAAU;AAC/B,KAAC,mBAAmB,mBAAmB,QAAQ,iBAAiB,WAAW;AAC3E,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AAmBA,IAAMC,UAAS;AAef,IAAMC,SAAQ;AAQd,IAAMC,QAAO;AAQb,IAAMC,QAAO;AAOb,IAAMC,QAAO;AAOb,IAAMC,SAAQ;AAYd,IAAMC,cAAa;AAMnB,IAAMC,mBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB;AAAA,IACxB,GAAG,cAAc;AAAA,IACjB,IAAI;AAAA,EACN;AACA,SAAO,gBAAkB,WAAW,UAAU;AAAA,IAC5C,GAAG;AAAA,IACH,UAAU;AAAA,EACZ,CAAC;AACH;;;AClvBA,IAAAC,SAAuB;AACvB,mBAAgC;AAChC,eAA0B;AAE1B,IAAI,WAAW,OAAO,aAAa;AAEnC,IAAI,OAAO,SAASC,QAAO;AAAC;AAC5B,IAAI,QAAQ,WAAW,+BAAkB;AAIzC,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,cAAc,EAAE,SAAS,MAAM,EAAE,SAAS,GAAG;AAC5D,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,KAAK,OAAO,MAAM,UAAU;AACnC,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,WAAW,EAAE,OAAQ,QAAO;AAChC,WAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG;AAC1B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AACpC,aAAO;AAAA,IACT;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,CAAC,CAAC,EAAE,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG;AACvC,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAM,MAAM,KAAK,CAAC;AAClB,UAAI,QAAQ,YAAY,EAAE,UAAU;AAClC;AAAA,MACF;AACA,UAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,MAAM,KAAK,MAAM;AAC1B;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,QAAQ,cAAc,eAAe;AACjD,SAAO,IAAI,oBAAoB;AACjC;AAEA,SAAS,WAAW,SAAS,OAAO;AAClC,QAAM,MAAM,OAAO,OAAO;AAC1B,SAAO,KAAK,MAAM,QAAQ,GAAG,IAAI;AACnC;AAEA,SAAS,aAAa,OAAO;AAC3B,QAAM,MAAY,cAAO,KAAK;AAC9B,QAAM,MAAM;AACV,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAMA,SAAS,YAAY,SAAS;AAC5B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,IAAI,CAAC;AAAA,IACL,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,MAAM,OAAO,IAAU,gBAAS;AAAA,IACrC,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,gBAAgB,CAAC;AAAA,IACjB,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,gBAAS,UAAU;AACzE,MAAI,CAAC,UAAU,kBAAkB,UAAU,GAAG;AAC5C,wBAAoB,UAAU;AAAA,EAChC;AACA,QAAM,CAAC,YAAY,aAAa,IAAU,gBAAS,IAAI;AACvD,QAAM,CAAC,WAAW,YAAY,IAAU,gBAAS,IAAI;AACrD,QAAM,eAAqB,mBAAY,UAAQ;AAC7C,QAAI,SAAS,aAAa,SAAS;AACjC,mBAAa,UAAU;AACvB,oBAAc,IAAI;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,cAAoB,mBAAY,UAAQ;AAC5C,QAAI,SAAS,YAAY,SAAS;AAChC,kBAAY,UAAU;AACtB,mBAAa,IAAI;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,cAAc,qBAAqB;AACzC,QAAM,aAAa,oBAAoB;AACvC,QAAM,eAAqB,cAAO,IAAI;AACtC,QAAM,cAAoB,cAAO,IAAI;AACrC,QAAM,UAAgB,cAAO,IAAI;AACjC,QAAM,0BAA0B,wBAAwB;AACxD,QAAM,0BAA0B,aAAa,oBAAoB;AACjE,QAAM,cAAc,aAAaA,SAAQ;AACzC,QAAM,UAAU,aAAa,IAAI;AACjC,QAAM,SAAe,mBAAY,MAAM;AACrC,QAAI,CAAC,aAAa,WAAW,CAAC,YAAY,SAAS;AACjD;AAAA,IACF;AACA,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd;AACA,QAAI,YAAY,SAAS;AACvB,aAAO,WAAW,YAAY;AAAA,IAChC;AACA,IAAAC,iBAAgB,aAAa,SAAS,YAAY,SAAS,MAAM,EAAE,KAAK,CAAAC,UAAQ;AAC9E,YAAM,WAAW;AAAA,QACf,GAAGA;AAAA;AAAA;AAAA;AAAA;AAAA,QAKH,cAAc,QAAQ,YAAY;AAAA,MACpC;AACA,UAAI,aAAa,WAAW,CAAC,UAAU,QAAQ,SAAS,QAAQ,GAAG;AACjE,gBAAQ,UAAU;AAClB,QAAS,mBAAU,MAAM;AACvB,kBAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,kBAAkB,WAAW,UAAU,aAAa,OAAO,CAAC;AAChE,QAAM,MAAM;AACV,QAAI,SAAS,SAAS,QAAQ,QAAQ,cAAc;AAClD,cAAQ,QAAQ,eAAe;AAC/B,cAAQ,CAAAA,WAAS;AAAA,QACf,GAAGA;AAAA,QACH,cAAc;AAAA,MAChB,EAAE;AAAA,IACJ;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,eAAqB,cAAO,KAAK;AACvC,QAAM,MAAM;AACV,iBAAa,UAAU;AACvB,WAAO,MAAM;AACX,mBAAa,UAAU;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,MAAM;AACV,QAAI,YAAa,cAAa,UAAU;AACxC,QAAI,WAAY,aAAY,UAAU;AACtC,QAAI,eAAe,YAAY;AAC7B,UAAI,wBAAwB,SAAS;AACnC,eAAO,wBAAwB,QAAQ,aAAa,YAAY,MAAM;AAAA,MACxE;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG,CAAC,aAAa,YAAY,QAAQ,yBAAyB,uBAAuB,CAAC;AACtF,QAAM,OAAa,eAAQ,OAAO;AAAA,IAChC,WAAW;AAAA,IACX,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,IAAI,CAAC,cAAc,WAAW,CAAC;AAC/B,QAAM,WAAiB,eAAQ,OAAO;AAAA,IACpC,WAAW;AAAA,IACX,UAAU;AAAA,EACZ,IAAI,CAAC,aAAa,UAAU,CAAC;AAC7B,QAAM,iBAAuB,eAAQ,MAAM;AACzC,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,QAAI,CAAC,SAAS,UAAU;AACtB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,QAAI,WAAW;AACb,aAAO;AAAA,QACL,GAAG;AAAA,QACH,WAAW,eAAe,IAAI,SAAS,IAAI;AAAA,QAC3C,GAAI,OAAO,SAAS,QAAQ,KAAK,OAAO;AAAA,UACtC,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AAAA,EACF,GAAG,CAAC,UAAU,WAAW,SAAS,UAAU,KAAK,GAAG,KAAK,CAAC,CAAC;AAC3D,SAAa,eAAQ,OAAO;AAAA,IAC1B,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,QAAQ,MAAM,UAAU,cAAc,CAAC;AACpD;AAQA,IAAM,UAAU,aAAW;AACzB,WAAS,MAAM,OAAO;AACpB,WAAO,CAAC,EAAE,eAAe,KAAK,OAAO,SAAS;AAAA,EAChD;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,GAAG,OAAO;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACrD,UAAI,WAAW,MAAM,OAAO,GAAG;AAC7B,YAAI,QAAQ,WAAW,MAAM;AAC3B,iBAAOC,OAAQ;AAAA,YACb,SAAS,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC,EAAE,GAAG,KAAK;AAAA,QACb;AACA,eAAO,CAAC;AAAA,MACV;AACA,UAAI,SAAS;AACX,eAAOA,OAAQ;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC,EAAE,GAAG,KAAK;AAAA,MACb;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AASA,IAAMC,UAAS,CAAC,SAAS,UAAU;AAAA,EACjC,GAAGA,QAAS,OAAO;AAAA,EACnB,SAAS,CAAC,SAAS,IAAI;AACzB;AAOA,IAAMC,SAAQ,CAAC,SAAS,UAAU;AAAA,EAChC,GAAGA,OAAQ,OAAO;AAAA,EAClB,SAAS,CAAC,SAAS,IAAI;AACzB;AAKA,IAAMC,cAAa,CAAC,SAAS,UAAU;AAAA,EACrC,GAAGA,YAAa,OAAO;AAAA,EACvB,SAAS,CAAC,SAAS,IAAI;AACzB;AAQA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAGA,MAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAQA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAGA,MAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAkBA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAGA,MAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAkBA,IAAMC,SAAQ,CAAC,SAAS,UAAU;AAAA,EAChC,GAAG,QAAQ,OAAO;AAAA,EAClB,SAAS,CAAC,SAAS,IAAI;AACzB;;;AChXA,IAAAC,SAAuB;AAyBW,IAAAC,sBAAA;AAlBlC,IAAM,OAAO;AAMb,IAAM,QAAc,kBAAqC,CAAC,OAAO,iBAAiB;AAChF,QAAM,EAAE,UAAU,QAAQ,IAAI,SAAS,GAAG,GAAG,WAAW,IAAI;AAC5D,aACE;IAAC,UAAU;IAAV;MACE,GAAG;MACJ,KAAK;MACL;MACA;MACA,SAAQ;MACR,qBAAoB;MAGnB,UAAA,MAAM,UAAU,eAAW,yBAAC,WAAA,EAAQ,QAAO,iBAAA,CAAiB;IAAA;EAC/D;AAEJ,CAAC;AAED,MAAM,cAAc;AAIpB,IAAM,OAAO;;;ANkBT,IAAAC,sBAAA;AAlBJ,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAM/E,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAK9F,IAAM,SAAgC,CAAC,UAAoC;AACzE,QAAM,EAAE,eAAe,SAAS,IAAI;AACpC,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAA4B,IAAI;AAClE,aACE,yBAAC,gBAAA,EAAe,OAAO,eAAe,QAAgB,gBAAgB,WACnE,SAAA,CACH;AAEJ;AAEA,OAAO,cAAc;AAMrB,IAAM,cAAc;AAQpB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,eAAe,YAAY,GAAG,YAAY,IAAI;AACtD,UAAM,UAAU,iBAAiB,aAAa,aAAa;AAC3D,UAAM,MAAY,cAA4B,IAAI;AAClD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AAEhD,IAAA,iBAAU,MAAM;AAIpB,cAAQ,gBAAe,yCAAY,YAAW,IAAI,OAAO;IAC3D,CAAC;AAED,WAAO,aAAa,WAAO,yBAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,aAAa,cAAc;AAM3B,IAAM,eAAe;AAUrB,IAAM,CAAC,uBAAuB,iBAAiB,IAC7C,oBAA+C,YAAY;AAoB7D,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;;AACxD,UAAM;MACJ;MACA,OAAO;MACP,aAAa;MACb,QAAQ;MACR,cAAc;MACd,eAAe;MACf,kBAAkB;MAClB,oBAAoB,CAAC;MACrB,kBAAkB,uBAAuB;MACzC,SAAS;MACT,mBAAmB;MACnB,yBAAyB;MACzB;MACA,GAAG;IACL,IAAI;AAEJ,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAE5D,UAAM,CAAC,SAAS,UAAU,IAAU,gBAAgC,IAAI;AACxE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAE7E,UAAM,CAACC,QAAO,QAAQ,IAAU,gBAAiC,IAAI;AACrE,UAAM,YAAY,QAAQA,MAAK;AAC/B,UAAM,cAAa,uCAAW,UAAS;AACvC,UAAM,eAAc,uCAAW,WAAU;AAEzC,UAAM,mBAAoB,QAAQ,UAAU,WAAW,MAAM,QAAQ;AAErE,UAAM,mBACJ,OAAO,yBAAyB,WAC5B,uBACA,EAAE,KAAK,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,GAAG,qBAAqB;AAEtE,UAAM,WAAW,MAAM,QAAQ,iBAAiB,IAAI,oBAAoB,CAAC,iBAAiB;AAC1F,UAAM,wBAAwB,SAAS,SAAS;AAEhD,UAAM,wBAAwB;MAC5B,SAAS;MACT,UAAU,SAAS,OAAO,SAAS;;MAEnC,aAAa;IACf;AAEA,UAAM,EAAE,MAAM,gBAAgB,WAAW,cAAc,eAAe,IAAI,YAAY;;MAEpF,UAAU;MACV,WAAW;MACX,sBAAsB,IAAI,SAAS;AACjC,cAAM,UAAU,WAAW,GAAG,MAAM;UAClC,gBAAgB,2BAA2B;QAC7C,CAAC;AACD,eAAO;MACT;MACA,UAAU;QACR,WAAW,QAAQ;MACrB;MACA,YAAY;QACVC,QAAO,EAAE,UAAU,aAAa,aAAa,eAAe,YAAY,CAAC;QACzE,mBACEC,OAAM;UACJ,UAAU;UACV,WAAW;UACX,SAAS,WAAW,YAAYC,YAAW,IAAI;UAC/C,GAAG;QACL,CAAC;QACH,mBAAmBC,MAAK,EAAE,GAAG,sBAAsB,CAAC;QACpDC,MAAK;UACH,GAAG;UACH,OAAO,CAAC,EAAE,UAAU,OAAO,gBAAgB,gBAAgB,MAAM;AAC/D,kBAAM,EAAE,OAAO,aAAa,QAAQ,aAAa,IAAI,MAAM;AAC3D,kBAAM,eAAe,SAAS,SAAS;AACvC,yBAAa,YAAY,kCAAkC,GAAG,cAAc,IAAI;AAChF,yBAAa,YAAY,mCAAmC,GAAG,eAAe,IAAI;AAClF,yBAAa,YAAY,+BAA+B,GAAG,WAAW,IAAI;AAC1E,yBAAa,YAAY,gCAAgC,GAAG,YAAY,IAAI;UAC9E;QACF,CAAC;QACDL,UAASA,OAAgB,EAAE,SAASA,QAAO,SAAS,aAAa,CAAC;QAClE,gBAAgB,EAAE,YAAY,YAAY,CAAC;QAC3C,oBAAoBM,MAAK,EAAE,UAAU,mBAAmB,GAAG,sBAAsB,CAAC;MACpF;IACF,CAAC;AAED,UAAM,CAAC,YAAY,WAAW,IAAI,6BAA6B,SAAS;AAExE,UAAM,eAAe,eAAe,QAAQ;AAC5C,qBAAgB,MAAM;AACpB,UAAI,cAAc;AAChB;MACF;IACF,GAAG,CAAC,cAAc,YAAY,CAAC;AAE/B,UAAM,UAAS,oBAAe,UAAf,mBAAsB;AACrC,UAAM,UAAS,oBAAe,UAAf,mBAAsB;AACrC,UAAM,sBAAoB,oBAAe,UAAf,mBAAsB,kBAAiB;AAEjE,UAAM,CAAC,eAAe,gBAAgB,IAAU,gBAAiB;AACjE,qBAAgB,MAAM;AACpB,UAAI,QAAS,kBAAiB,OAAO,iBAAiB,OAAO,EAAE,MAAM;IACvE,GAAG,CAAC,OAAO,CAAC;AAEZ,eACE;MAAC;MAAA;QACC,KAAK,KAAK;QACV,qCAAkC;QAClC,OAAO;UACL,GAAG;UACH,WAAW,eAAe,eAAe,YAAY;;UACrD,UAAU;UACV,QAAQ;UACR,CAAC,iCAAwC,GAAG;aAC1C,oBAAe,oBAAf,mBAAgC;aAChC,oBAAe,oBAAf,mBAAgC;UAClC,EAAE,KAAK,GAAG;;;;UAKV,KAAI,oBAAe,SAAf,mBAAqB,oBAAmB;YAC1C,YAAY;YACZ,eAAe;UACjB;QACF;QAIA,KAAK,MAAM;QAEX,cAAA;UAAC;UAAA;YACC,OAAO;YACP;YACA,eAAe;YACf;YACA;YACA,iBAAiB;YAEjB,cAAA;cAAC,UAAU;cAAV;gBACC,aAAW;gBACX,cAAY;gBACX,GAAG;gBACJ,KAAK;gBACL,OAAO;kBACL,GAAG,aAAa;;;kBAGhB,WAAW,CAAC,eAAe,SAAS;gBACtC;cAAA;YACF;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,aAAa;AAEnB,IAAM,gBAAoC;EACxC,KAAK;EACL,OAAO;EACP,QAAQ;EACR,MAAM;AACR;AAMA,IAAM,cAAoB,kBAAiD,SAASC,aAClF,OACA,cACA;AACA,QAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,QAAM,iBAAiB,kBAAkB,YAAY,aAAa;AAClE,QAAM,WAAW,cAAc,eAAe,UAAU;AAExD;;;;QAIE;MAAC;MAAA;QACC,KAAK,eAAe;QACpB,OAAO;UACL,UAAU;UACV,MAAM,eAAe;UACrB,KAAK,eAAe;UACpB,CAAC,QAAQ,GAAG;UACZ,iBAAiB;YACf,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;UACR,EAAE,eAAe,UAAU;UAC3B,WAAW;YACT,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;UACR,EAAE,eAAe,UAAU;UAC3B,YAAY,eAAe,kBAAkB,WAAW;QAC1D;QAEA,cAAA;UAAgB;UAAf;YACE,GAAG;YACJ,KAAK;YACL,OAAO;cACL,GAAG,WAAW;;cAEd,SAAS;YACX;UAAA;QACF;MAAA;IACF;;AAEJ,CAAC;AAED,YAAY,cAAc;AAI1B,SAAS,UAAa,OAA6B;AACjD,SAAO,UAAU;AACnB;AAEA,IAAM,kBAAkB,CAAC,aAAsE;EAC7F,MAAM;EACN;EACA,GAAG,MAAM;;AACP,UAAM,EAAE,WAAW,OAAO,eAAe,IAAI;AAE7C,UAAM,sBAAoB,oBAAe,UAAf,mBAAsB,kBAAiB;AACjE,UAAM,gBAAgB;AACtB,UAAM,aAAa,gBAAgB,IAAI,QAAQ;AAC/C,UAAM,cAAc,gBAAgB,IAAI,QAAQ;AAEhD,UAAM,CAAC,YAAY,WAAW,IAAI,6BAA6B,SAAS;AACxE,UAAM,eAAe,EAAE,OAAO,MAAM,QAAQ,OAAO,KAAK,OAAO,EAAE,WAAW;AAE5E,UAAM,kBAAgB,oBAAe,UAAf,mBAAsB,MAAK,KAAK,aAAa;AACnE,UAAM,kBAAgB,oBAAe,UAAf,mBAAsB,MAAK,KAAK,cAAc;AAEpE,QAAI,IAAI;AACR,QAAI,IAAI;AAER,QAAI,eAAe,UAAU;AAC3B,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAClD,UAAI,GAAG,CAAC,WAAW;IACrB,WAAW,eAAe,OAAO;AAC/B,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAClD,UAAI,GAAG,MAAM,SAAS,SAAS,WAAW;IAC5C,WAAW,eAAe,SAAS;AACjC,UAAI,GAAG,CAAC,WAAW;AACnB,UAAI,gBAAgB,eAAe,GAAG,YAAY;IACpD,WAAW,eAAe,QAAQ;AAChC,UAAI,GAAG,MAAM,SAAS,QAAQ,WAAW;AACzC,UAAI,gBAAgB,eAAe,GAAG,YAAY;IACpD;AACA,WAAO,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE;EAC1B;AACF;AAEA,SAAS,6BAA6B,WAAsB;AAC1D,QAAM,CAAC,MAAM,QAAQ,QAAQ,IAAI,UAAU,MAAM,GAAG;AACpD,SAAO,CAAC,MAAc,KAAc;AACtC;AAEA,IAAMC,QAAO;AACb,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAMC,SAAQ;;;AOnZd,IAAAC,SAAuB;AACvB,IAAAC,oBAAqB;AAyBO,IAAAC,sBAAA;AAjB5B,IAAM,cAAc;AAWpB,IAAM,SAAe,kBAAuC,CAAC,OAAO,iBAAiB;;AACnF,QAAM,EAAE,WAAW,eAAe,GAAG,YAAY,IAAI;AACrD,QAAM,CAAC,SAAS,UAAU,IAAU,gBAAS,KAAK;AAClD,mBAAgB,MAAM,WAAW,IAAI,GAAG,CAAC,CAAC;AAC1C,QAAM,YAAY,iBAAkB,aAAW,8CAAY,aAAZ,mBAAsB;AACrE,SAAO,YACH,kBAAAC,QAAS,iBAAa,yBAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc,GAAI,SAAS,IACtF;AACN,CAAC;AAED,OAAO,cAAc;;;AC9BrB,IAAAC,SAAuB;AA8BjB,IAAAC,sBAAA;AAvBN,IAAM,yBAAyB,OAAO,OAAO;;EAE3C,UAAU;EACV,QAAQ;EACR,OAAO;EACP,QAAQ;EACR,SAAS;EACT,QAAQ;EACR,UAAU;EACV,MAAM;EACN,YAAY;EACZ,UAAU;AACZ,CAAC;AAED,IAAMC,QAAO;AAMb,IAAM,iBAAuB;EAC3B,CAAC,OAAO,iBAAiB;AACvB,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,OAAO,EAAE,GAAG,wBAAwB,GAAG,MAAM,MAAM;MAAA;IACrD;EAEJ;AACF;AAEA,eAAe,cAAcA;;;ACvC7B,IAAI,mBAAmB,SAAU,gBAAgB;AAC7C,MAAI,OAAO,aAAa,aAAa;AACjC,WAAO;AAAA,EACX;AACA,MAAI,eAAe,MAAM,QAAQ,cAAc,IAAI,eAAe,CAAC,IAAI;AACvE,SAAO,aAAa,cAAc;AACtC;AACA,IAAI,aAAa,oBAAI,QAAQ;AAC7B,IAAI,oBAAoB,oBAAI,QAAQ;AACpC,IAAI,YAAY,CAAC;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa,SAAU,MAAM;AAC7B,SAAO,SAAS,KAAK,QAAQ,WAAW,KAAK,UAAU;AAC3D;AACA,IAAI,iBAAiB,SAAU,QAAQ,SAAS;AAC5C,SAAO,QACF,IAAI,SAAU,QAAQ;AACvB,QAAI,OAAO,SAAS,MAAM,GAAG;AACzB,aAAO;AAAA,IACX;AACA,QAAI,kBAAkB,WAAW,MAAM;AACvC,QAAI,mBAAmB,OAAO,SAAS,eAAe,GAAG;AACrD,aAAO;AAAA,IACX;AACA,YAAQ,MAAM,eAAe,QAAQ,2BAA2B,QAAQ,iBAAiB;AACzF,WAAO;AAAA,EACX,CAAC,EACI,OAAO,SAAU,GAAG;AAAE,WAAO,QAAQ,CAAC;AAAA,EAAG,CAAC;AACnD;AASA,IAAI,yBAAyB,SAAU,gBAAgB,YAAY,YAAY,kBAAkB;AAC7F,MAAI,UAAU,eAAe,YAAY,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAC,cAAc,CAAC;AAC1G,MAAI,CAAC,UAAU,UAAU,GAAG;AACxB,cAAU,UAAU,IAAI,oBAAI,QAAQ;AAAA,EACxC;AACA,MAAI,gBAAgB,UAAU,UAAU;AACxC,MAAI,cAAc,CAAC;AACnB,MAAI,iBAAiB,oBAAI,IAAI;AAC7B,MAAI,iBAAiB,IAAI,IAAI,OAAO;AACpC,MAAI,OAAO,SAAU,IAAI;AACrB,QAAI,CAAC,MAAM,eAAe,IAAI,EAAE,GAAG;AAC/B;AAAA,IACJ;AACA,mBAAe,IAAI,EAAE;AACrB,SAAK,GAAG,UAAU;AAAA,EACtB;AACA,UAAQ,QAAQ,IAAI;AACpB,MAAI,OAAO,SAAU,QAAQ;AACzB,QAAI,CAAC,UAAU,eAAe,IAAI,MAAM,GAAG;AACvC;AAAA,IACJ;AACA,UAAM,UAAU,QAAQ,KAAK,OAAO,UAAU,SAAU,MAAM;AAC1D,UAAI,eAAe,IAAI,IAAI,GAAG;AAC1B,aAAK,IAAI;AAAA,MACb,OACK;AACD,YAAI;AACA,cAAI,OAAO,KAAK,aAAa,gBAAgB;AAC7C,cAAI,gBAAgB,SAAS,QAAQ,SAAS;AAC9C,cAAI,gBAAgB,WAAW,IAAI,IAAI,KAAK,KAAK;AACjD,cAAI,eAAe,cAAc,IAAI,IAAI,KAAK,KAAK;AACnD,qBAAW,IAAI,MAAM,YAAY;AACjC,wBAAc,IAAI,MAAM,WAAW;AACnC,sBAAY,KAAK,IAAI;AACrB,cAAI,iBAAiB,KAAK,eAAe;AACrC,8BAAkB,IAAI,MAAM,IAAI;AAAA,UACpC;AACA,cAAI,gBAAgB,GAAG;AACnB,iBAAK,aAAa,YAAY,MAAM;AAAA,UACxC;AACA,cAAI,CAAC,eAAe;AAChB,iBAAK,aAAa,kBAAkB,MAAM;AAAA,UAC9C;AAAA,QACJ,SACO,GAAG;AACN,kBAAQ,MAAM,mCAAmC,MAAM,CAAC;AAAA,QAC5D;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACA,OAAK,UAAU;AACf,iBAAe,MAAM;AACrB;AACA,SAAO,WAAY;AACf,gBAAY,QAAQ,SAAU,MAAM;AAChC,UAAI,eAAe,WAAW,IAAI,IAAI,IAAI;AAC1C,UAAI,cAAc,cAAc,IAAI,IAAI,IAAI;AAC5C,iBAAW,IAAI,MAAM,YAAY;AACjC,oBAAc,IAAI,MAAM,WAAW;AACnC,UAAI,CAAC,cAAc;AACf,YAAI,CAAC,kBAAkB,IAAI,IAAI,GAAG;AAC9B,eAAK,gBAAgB,gBAAgB;AAAA,QACzC;AACA,0BAAkB,OAAO,IAAI;AAAA,MACjC;AACA,UAAI,CAAC,aAAa;AACd,aAAK,gBAAgB,UAAU;AAAA,MACnC;AAAA,IACJ,CAAC;AACD;AACA,QAAI,CAAC,WAAW;AAEZ,mBAAa,oBAAI,QAAQ;AACzB,mBAAa,oBAAI,QAAQ;AACzB,0BAAoB,oBAAI,QAAQ;AAChC,kBAAY,CAAC;AAAA,IACjB;AAAA,EACJ;AACJ;AAQO,IAAI,aAAa,SAAU,gBAAgB,YAAY,YAAY;AACtE,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAoB;AAC9D,MAAI,UAAU,MAAM,KAAK,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAC,cAAc,CAAC;AAC1F,MAAI,mBAAmB,cAAc,iBAAiB,cAAc;AACpE,MAAI,CAAC,kBAAkB;AACnB,WAAO,WAAY;AAAE,aAAO;AAAA,IAAM;AAAA,EACtC;AAGA,UAAQ,KAAK,MAAM,SAAS,MAAM,KAAK,iBAAiB,iBAAiB,qBAAqB,CAAC,CAAC;AAChG,SAAO,uBAAuB,SAAS,kBAAkB,YAAY,aAAa;AACtF;;;ACvGO,IAAI,WAAW,WAAW;AAC/B,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACT;AAiKO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;;;AC5NA,IAAAC,UAAuB;;;ACAvB,IAAAC,UAAuB;;;ACDhB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,wBAAwB;AAK5B,IAAI,yBAAyB;;;ACM7B,SAAS,UAAU,KAAK,OAAO;AAClC,MAAI,OAAO,QAAQ,YAAY;AAC3B,QAAI,KAAK;AAAA,EACb,WACS,KAAK;AACV,QAAI,UAAU;AAAA,EAClB;AACA,SAAO;AACX;;;ACrBA,IAAAC,gBAAyB;AAelB,SAASC,gBAAe,cAAc,UAAU;AACnD,MAAI,UAAM,wBAAS,WAAY;AAAE,WAAQ;AAAA;AAAA,MAErC,OAAO;AAAA;AAAA,MAEP;AAAA;AAAA,MAEA,QAAQ;AAAA,QACJ,IAAI,UAAU;AACV,iBAAO,IAAI;AAAA,QACf;AAAA,QACA,IAAI,QAAQ,OAAO;AACf,cAAI,OAAO,IAAI;AACf,cAAI,SAAS,OAAO;AAChB,gBAAI,QAAQ;AACZ,gBAAI,SAAS,OAAO,IAAI;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EAAI,CAAC,EAAE,CAAC;AAER,MAAI,WAAW;AACf,SAAO,IAAI;AACf;;;ACtCA,IAAAC,UAAuB;AAGvB,IAAI,4BAA4B,OAAO,WAAW,cAAoB,0BAAwB;AAC9F,IAAI,gBAAgB,oBAAI,QAAQ;AAezB,SAAS,aAAa,MAAM,cAAc;AAC7C,MAAI,cAAcC,gBAAe,gBAAgB,MAAM,SAAU,UAAU;AACvE,WAAO,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,UAAU,KAAK,QAAQ;AAAA,IAAG,CAAC;AAAA,EAC3E,CAAC;AAED,4BAA0B,WAAY;AAClC,QAAI,WAAW,cAAc,IAAI,WAAW;AAC5C,QAAI,UAAU;AACV,UAAI,aAAa,IAAI,IAAI,QAAQ;AACjC,UAAI,aAAa,IAAI,IAAI,IAAI;AAC7B,UAAI,YAAY,YAAY;AAC5B,iBAAW,QAAQ,SAAU,KAAK;AAC9B,YAAI,CAAC,WAAW,IAAI,GAAG,GAAG;AACtB,oBAAU,KAAK,IAAI;AAAA,QACvB;AAAA,MACJ,CAAC;AACD,iBAAW,QAAQ,SAAU,KAAK;AAC9B,YAAI,CAAC,WAAW,IAAI,GAAG,GAAG;AACtB,oBAAU,KAAK,SAAS;AAAA,QAC5B;AAAA,MACJ,CAAC;AAAA,IACL;AACA,kBAAc,IAAI,aAAa,IAAI;AAAA,EACvC,GAAG,CAAC,IAAI,CAAC;AACT,SAAO;AACX;;;AC3CA,IAAAC,UAAuB;;;ACDvB,IAAAC,gBAAoC;;;ACCpC,SAAS,KAAK,GAAG;AACb,SAAO;AACX;AACA,SAAS,kBAAkB,UAAU,YAAY;AAC7C,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAM;AAChD,MAAI,SAAS,CAAC;AACd,MAAI,WAAW;AACf,MAAI,SAAS;AAAA,IACT,MAAM,WAAY;AACd,UAAI,UAAU;AACV,cAAM,IAAI,MAAM,kGAAkG;AAAA,MACtH;AACA,UAAI,OAAO,QAAQ;AACf,eAAO,OAAO,OAAO,SAAS,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACX;AAAA,IACA,WAAW,SAAU,MAAM;AACvB,UAAI,OAAO,WAAW,MAAM,QAAQ;AACpC,aAAO,KAAK,IAAI;AAChB,aAAO,WAAY;AACf,iBAAS,OAAO,OAAO,SAAU,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAM,CAAC;AAAA,MAC9D;AAAA,IACJ;AAAA,IACA,kBAAkB,SAAU,IAAI;AAC5B,iBAAW;AACX,aAAO,OAAO,QAAQ;AAClB,YAAI,MAAM;AACV,iBAAS,CAAC;AACV,YAAI,QAAQ,EAAE;AAAA,MAClB;AACA,eAAS;AAAA,QACL,MAAM,SAAU,GAAG;AAAE,iBAAO,GAAG,CAAC;AAAA,QAAG;AAAA,QACnC,QAAQ,WAAY;AAAE,iBAAO;AAAA,QAAQ;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,cAAc,SAAU,IAAI;AACxB,iBAAW;AACX,UAAI,eAAe,CAAC;AACpB,UAAI,OAAO,QAAQ;AACf,YAAI,MAAM;AACV,iBAAS,CAAC;AACV,YAAI,QAAQ,EAAE;AACd,uBAAe;AAAA,MACnB;AACA,UAAI,eAAe,WAAY;AAC3B,YAAIC,OAAM;AACV,uBAAe,CAAC;AAChB,QAAAA,KAAI,QAAQ,EAAE;AAAA,MAClB;AACA,UAAI,QAAQ,WAAY;AAAE,eAAO,QAAQ,QAAQ,EAAE,KAAK,YAAY;AAAA,MAAG;AACvE,YAAM;AACN,eAAS;AAAA,QACL,MAAM,SAAU,GAAG;AACf,uBAAa,KAAK,CAAC;AACnB,gBAAM;AAAA,QACV;AAAA,QACA,QAAQ,SAAU,QAAQ;AACtB,yBAAe,aAAa,OAAO,MAAM;AACzC,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAMO,SAAS,oBAAoB,SAAS;AACzC,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,kBAAkB,IAAI;AACnC,SAAO,UAAU,SAAS,EAAE,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO;AAC9D,SAAO;AACX;;;AC5EA,IAAAC,UAAuB;AACvB,IAAAC,gBAAkE;;;ACDlE,IAAAC,UAAuB;AACvB,IAAI,UAAU,SAAU,IAAI;AACxB,MAAI,UAAU,GAAG,SAAS,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC;AACvD,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACxF;AACA,MAAI,SAAS,QAAQ,KAAK;AAC1B,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC9C;AACA,SAAa,sBAAc,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC;AACzD;AACA,QAAQ,kBAAkB;AACnB,SAAS,cAAc,QAAQ,UAAU;AAC5C,SAAO,UAAU,QAAQ;AACzB,SAAO;AACX;;;AChBO,IAAI,YAAY,oBAAoB;;;AVI3C,IAAI,UAAU,WAAY;AACtB;AACJ;AAIA,IAAI,eAAqB,mBAAW,SAAU,OAAO,WAAW;AAC5D,MAAI,MAAY,eAAO,IAAI;AAC3B,MAAI,KAAW,iBAAS;AAAA,IACpB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,EACxB,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAC1C,MAAI,eAAe,MAAM,cAAc,WAAW,MAAM,UAAU,YAAY,MAAM,WAAW,kBAAkB,MAAM,iBAAiB,UAAU,MAAM,SAAS,SAAS,MAAM,QAAQ,UAAU,MAAM,SAAS,aAAa,MAAM,YAAY,cAAc,MAAM,aAAa,QAAQ,MAAM,OAAO,iBAAiB,MAAM,gBAAgB,KAAK,MAAM,IAAI,YAAY,OAAO,SAAS,QAAQ,IAAI,UAAU,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC,gBAAgB,YAAY,aAAa,mBAAmB,WAAW,UAAU,WAAW,cAAc,eAAe,SAAS,kBAAkB,MAAM,SAAS,CAAC;AACvlB,MAAIC,WAAU;AACd,MAAI,eAAe,aAAa,CAAC,KAAK,SAAS,CAAC;AAChD,MAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,SAAS;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,WAAkB,sBAAcA,UAAS,EAAE,SAAS,WAAW,iBAAkC,QAAgB,YAAwB,aAA0B,OAAc,cAA4B,gBAAgB,CAAC,CAAC,gBAAgB,SAAS,KAAK,QAAiB,CAAC;AAAA,IAC/Q,eAAsB,qBAAmB,iBAAS,KAAK,QAAQ,GAAG,SAAS,SAAS,CAAC,GAAG,cAAc,GAAG,EAAE,KAAK,aAAa,CAAC,CAAC,IAAY,sBAAc,WAAW,SAAS,CAAC,GAAG,gBAAgB,EAAE,WAAsB,KAAK,aAAa,CAAC,GAAG,QAAQ;AAAA,EAAE;AACjQ,CAAC;AACD,aAAa,eAAe;AAAA,EACxB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,OAAO;AACX;AACA,aAAa,aAAa;AAAA,EACtB,WAAW;AAAA,EACX,WAAW;AACf;;;AWjCA,IAAAC,UAAuB;;;ACDvB,IAAAC,UAAuB;;;ACAvB,IAAAC,UAAuB;;;ACAvB,IAAI;AAIG,IAAI,WAAW,WAAY;AAC9B,MAAI,cAAc;AACd,WAAO;AAAA,EACX;AACA,MAAI,OAAO,sBAAsB,aAAa;AAC1C,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACXA,SAAS,eAAe;AACpB,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,SAAS,cAAc,OAAO;AACxC,MAAI,OAAO;AACX,MAAI,QAAQ,SAAS;AACrB,MAAI,OAAO;AACP,QAAI,aAAa,SAAS,KAAK;AAAA,EACnC;AACA,SAAO;AACX;AACA,SAAS,aAAa,KAAK,KAAK;AAE5B,MAAI,IAAI,YAAY;AAEhB,QAAI,WAAW,UAAU;AAAA,EAC7B,OACK;AACD,QAAI,YAAY,SAAS,eAAe,GAAG,CAAC;AAAA,EAChD;AACJ;AACA,SAAS,eAAe,KAAK;AACzB,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,OAAK,YAAY,GAAG;AACxB;AACO,IAAI,sBAAsB,WAAY;AACzC,MAAI,UAAU;AACd,MAAI,aAAa;AACjB,SAAO;AAAA,IACH,KAAK,SAAU,OAAO;AAClB,UAAI,WAAW,GAAG;AACd,YAAK,aAAa,aAAa,GAAI;AAC/B,uBAAa,YAAY,KAAK;AAC9B,yBAAe,UAAU;AAAA,QAC7B;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,IACA,QAAQ,WAAY;AAChB;AACA,UAAI,CAAC,WAAW,YAAY;AACxB,mBAAW,cAAc,WAAW,WAAW,YAAY,UAAU;AACrE,qBAAa;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACJ;;;AFpCO,IAAI,qBAAqB,WAAY;AACxC,MAAI,QAAQ,oBAAoB;AAChC,SAAO,SAAU,QAAQ,WAAW;AAChC,IAAM,kBAAU,WAAY;AACxB,YAAM,IAAI,MAAM;AAChB,aAAO,WAAY;AACf,cAAM,OAAO;AAAA,MACjB;AAAA,IACJ,GAAG,CAAC,UAAU,SAAS,CAAC;AAAA,EAC5B;AACJ;;;AGdO,IAAI,iBAAiB,WAAY;AACpC,MAAI,WAAW,mBAAmB;AAClC,MAAI,QAAQ,SAAU,IAAI;AACtB,QAAI,SAAS,GAAG,QAAQ,UAAU,GAAG;AACrC,aAAS,QAAQ,OAAO;AACxB,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACfO,IAAI,UAAU;AAAA,EACjB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AACT;AACA,IAAI,QAAQ,SAAU,GAAG;AAAE,SAAO,SAAS,KAAK,IAAI,EAAE,KAAK;AAAG;AAC9D,IAAI,YAAY,SAAU,SAAS;AAC/B,MAAI,KAAK,OAAO,iBAAiB,SAAS,IAAI;AAC9C,MAAI,OAAO,GAAG,YAAY,YAAY,gBAAgB,YAAY;AAClE,MAAI,MAAM,GAAG,YAAY,YAAY,eAAe,WAAW;AAC/D,MAAI,QAAQ,GAAG,YAAY,YAAY,iBAAiB,aAAa;AACrE,SAAO,CAAC,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC;AACjD;AACO,IAAI,cAAc,SAAU,SAAS;AACxC,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAU;AAC9C,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,UAAU,UAAU,OAAO;AAC/B,MAAI,gBAAgB,SAAS,gBAAgB;AAC7C,MAAI,cAAc,OAAO;AACzB,SAAO;AAAA,IACH,MAAM,QAAQ,CAAC;AAAA,IACf,KAAK,QAAQ,CAAC;AAAA,IACd,OAAO,QAAQ,CAAC;AAAA,IAChB,KAAK,KAAK,IAAI,GAAG,cAAc,gBAAgB,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC;AAAA,EAC1E;AACJ;;;ALxBA,IAAI,QAAQ,eAAe;AACpB,IAAI,gBAAgB;AAI3B,IAAI,YAAY,SAAU,IAAI,eAAe,SAAS,WAAW;AAC7D,MAAI,OAAO,GAAG,MAAM,MAAM,GAAG,KAAK,QAAQ,GAAG,OAAO,MAAM,GAAG;AAC7D,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAU;AAC9C,SAAO,QAAQ,OAAO,uBAAuB,0BAA0B,EAAE,OAAO,WAAW,uBAAuB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,eAAe,4BAA4B,EAAE,OAAO,WAAW,4CAA4C,EAAE,OAAO;AAAA,IACnS,iBAAiB,sBAAsB,OAAO,WAAW,GAAG;AAAA,IAC5D,YAAY,YACR,uBAAuB,OAAO,MAAM,wBAAwB,EAAE,OAAO,KAAK,0BAA0B,EAAE,OAAO,OAAO,gEAAgE,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,SAAS;AAAA,IACxO,YAAY,aAAa,kBAAkB,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,GAAG;AAAA,EACvF,EACK,OAAO,OAAO,EACd,KAAK,EAAE,GAAG,gBAAgB,EAAE,OAAO,oBAAoB,iBAAiB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,wBAAwB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,IAAI,EAAE,OAAO,oBAAoB,mBAAmB,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,IAAI,EAAE,OAAO,oBAAoB,0BAA0B,EAAE,OAAO,WAAW,qBAAqB,EAAE,OAAO,eAAe,WAAW,EAAE,OAAO,wBAAwB,IAAI,EAAE,OAAO,KAAK,YAAY;AAC/kB;AACA,IAAI,uBAAuB,WAAY;AACnC,MAAI,UAAU,SAAS,SAAS,KAAK,aAAa,aAAa,KAAK,KAAK,EAAE;AAC3E,SAAO,SAAS,OAAO,IAAI,UAAU;AACzC;AACO,IAAI,mBAAmB,WAAY;AACtC,EAAM,kBAAU,WAAY;AACxB,aAAS,KAAK,aAAa,gBAAgB,qBAAqB,IAAI,GAAG,SAAS,CAAC;AACjF,WAAO,WAAY;AACf,UAAI,aAAa,qBAAqB,IAAI;AAC1C,UAAI,cAAc,GAAG;AACjB,iBAAS,KAAK,gBAAgB,aAAa;AAAA,MAC/C,OACK;AACD,iBAAS,KAAK,aAAa,eAAe,WAAW,SAAS,CAAC;AAAA,MACnE;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACT;AAIO,IAAI,kBAAkB,SAAU,IAAI;AACvC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,WAAW;AACpH,mBAAiB;AAMjB,MAAI,MAAY,gBAAQ,WAAY;AAAE,WAAO,YAAY,OAAO;AAAA,EAAG,GAAG,CAAC,OAAO,CAAC;AAC/E,SAAa,sBAAc,OAAO,EAAE,QAAQ,UAAU,KAAK,CAAC,YAAY,SAAS,CAAC,cAAc,eAAe,EAAE,EAAE,CAAC;AACxH;;;AMpDA,IAAI,mBAAmB;AACvB,IAAI,OAAO,WAAW,aAAa;AAC/B,MAAI;AACI,cAAU,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,MAC/C,KAAK,WAAY;AACb,2BAAmB;AACnB,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAED,WAAO,iBAAiB,QAAQ,SAAS,OAAO;AAEhD,WAAO,oBAAoB,QAAQ,SAAS,OAAO;AAAA,EACvD,SACO,KAAK;AACR,uBAAmB;AAAA,EACvB;AACJ;AAdY;AAeL,IAAI,aAAa,mBAAmB,EAAE,SAAS,MAAM,IAAI;;;AClBhE,IAAI,uBAAuB,SAAU,MAAM;AAEvC,SAAO,KAAK,YAAY;AAC5B;AACA,IAAI,uBAAuB,SAAU,MAAM,UAAU;AACjD,MAAI,EAAE,gBAAgB,UAAU;AAC5B,WAAO;AAAA,EACX;AACA,MAAI,SAAS,OAAO,iBAAiB,IAAI;AACzC;AAAA;AAAA,IAEA,OAAO,QAAQ,MAAM;AAAA,IAEjB,EAAE,OAAO,cAAc,OAAO,aAAa,CAAC,qBAAqB,IAAI,KAAK,OAAO,QAAQ,MAAM;AAAA;AACvG;AACA,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AAChG,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AACzF,IAAI,0BAA0B,SAAU,MAAM,MAAM;AACvD,MAAI,gBAAgB,KAAK;AACzB,MAAI,UAAU;AACd,KAAG;AAEC,QAAI,OAAO,eAAe,eAAe,mBAAmB,YAAY;AACpE,gBAAU,QAAQ;AAAA,IACtB;AACA,QAAI,eAAe,uBAAuB,MAAM,OAAO;AACvD,QAAI,cAAc;AACd,UAAI,KAAK,mBAAmB,MAAM,OAAO,GAAG,eAAe,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AACrF,UAAI,eAAe,cAAc;AAC7B,eAAO;AAAA,MACX;AAAA,IACJ;AACA,cAAU,QAAQ;AAAA,EACtB,SAAS,WAAW,YAAY,cAAc;AAC9C,SAAO;AACX;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,eAAe,GAAG;AAChF,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,cAAc,GAAG;AAC/E,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,yBAAyB,SAAU,MAAM,MAAM;AAC/C,SAAO,SAAS,MAAM,wBAAwB,IAAI,IAAI,wBAAwB,IAAI;AACtF;AACA,IAAI,qBAAqB,SAAU,MAAM,MAAM;AAC3C,SAAO,SAAS,MAAM,oBAAoB,IAAI,IAAI,oBAAoB,IAAI;AAC9E;AACA,IAAI,qBAAqB,SAAU,MAAM,WAAW;AAMhD,SAAO,SAAS,OAAO,cAAc,QAAQ,KAAK;AACtD;AACO,IAAI,eAAe,SAAU,MAAM,WAAW,OAAO,aAAa,cAAc;AACnF,MAAI,kBAAkB,mBAAmB,MAAM,OAAO,iBAAiB,SAAS,EAAE,SAAS;AAC3F,MAAI,QAAQ,kBAAkB;AAE9B,MAAI,SAAS,MAAM;AACnB,MAAI,eAAe,UAAU,SAAS,MAAM;AAC5C,MAAI,qBAAqB;AACzB,MAAI,kBAAkB,QAAQ;AAC9B,MAAI,kBAAkB;AACtB,MAAI,qBAAqB;AACzB,KAAG;AACC,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,QAAI,KAAK,mBAAmB,MAAM,MAAM,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC9F,QAAI,gBAAgB,WAAW,WAAW,kBAAkB;AAC5D,QAAI,YAAY,eAAe;AAC3B,UAAI,uBAAuB,MAAM,MAAM,GAAG;AACtC,2BAAmB;AACnB,8BAAsB;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,WAAW,OAAO;AAGtB,aAAU,YAAY,SAAS,aAAa,KAAK,yBAAyB,SAAS,OAAO;AAAA,EAC9F;AAAA;AAAA,IAEC,CAAC,gBAAgB,WAAW,SAAS;AAAA,IAEjC,iBAAiB,UAAU,SAAS,MAAM,KAAK,cAAc;AAAA;AAElE,MAAI,oBACE,gBAAgB,KAAK,IAAI,eAAe,IAAI,KAAO,CAAC,gBAAgB,QAAQ,kBAAmB;AACjG,yBAAqB;AAAA,EACzB,WACS,CAAC,oBACJ,gBAAgB,KAAK,IAAI,kBAAkB,IAAI,KAAO,CAAC,gBAAgB,CAAC,QAAQ,qBAAsB;AACxG,yBAAqB;AAAA,EACzB;AACA,SAAO;AACX;;;ARrGO,IAAI,aAAa,SAAU,OAAO;AACrC,SAAO,oBAAoB,QAAQ,CAAC,MAAM,eAAe,CAAC,EAAE,SAAS,MAAM,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AACjH;AACO,IAAI,aAAa,SAAU,OAAO;AAAE,SAAO,CAAC,MAAM,QAAQ,MAAM,MAAM;AAAG;AAChF,IAAI,aAAa,SAAU,KAAK;AAC5B,SAAO,OAAO,aAAa,MAAM,IAAI,UAAU;AACnD;AACA,IAAI,eAAe,SAAU,GAAG,GAAG;AAAE,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG;AAC5E,IAAI,gBAAgB,SAAU,IAAI;AAAE,SAAO,4BAA4B,OAAO,IAAI,mDAAmD,EAAE,OAAO,IAAI,2BAA2B;AAAG;AAChL,IAAI,YAAY;AAChB,IAAI,YAAY,CAAC;AACV,SAAS,oBAAoB,OAAO;AACvC,MAAI,qBAA2B,eAAO,CAAC,CAAC;AACxC,MAAI,gBAAsB,eAAO,CAAC,GAAG,CAAC,CAAC;AACvC,MAAI,aAAmB,eAAO;AAC9B,MAAI,KAAW,iBAAS,WAAW,EAAE,CAAC;AACtC,MAAIC,SAAc,iBAAS,cAAc,EAAE,CAAC;AAC5C,MAAI,YAAkB,eAAO,KAAK;AAClC,EAAM,kBAAU,WAAY;AACxB,cAAU,UAAU;AAAA,EACxB,GAAG,CAAC,KAAK,CAAC;AACV,EAAM,kBAAU,WAAY;AACxB,QAAI,MAAM,OAAO;AACb,eAAS,KAAK,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAC7D,UAAI,UAAU,cAAc,CAAC,MAAM,QAAQ,OAAO,IAAI,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU,GAAG,IAAI,EAAE,OAAO,OAAO;AAC/G,cAAQ,QAAQ,SAAU,IAAI;AAAE,eAAO,GAAG,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAAA,MAAG,CAAC;AAC7F,aAAO,WAAY;AACf,iBAAS,KAAK,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAChE,gBAAQ,QAAQ,SAAU,IAAI;AAAE,iBAAO,GAAG,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAAA,QAAG,CAAC;AAAA,MACpG;AAAA,IACJ;AACA;AAAA,EACJ,GAAG,CAAC,MAAM,OAAO,MAAM,QAAQ,SAAS,MAAM,MAAM,CAAC;AACrD,MAAI,oBAA0B,oBAAY,SAAU,OAAO,QAAQ;AAC/D,QAAK,aAAa,SAAS,MAAM,QAAQ,WAAW,KAAO,MAAM,SAAS,WAAW,MAAM,SAAU;AACjG,aAAO,CAAC,UAAU,QAAQ;AAAA,IAC9B;AACA,QAAI,QAAQ,WAAW,KAAK;AAC5B,QAAI,aAAa,cAAc;AAC/B,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI;AACJ,QAAI,SAAS,MAAM;AACnB,QAAI,gBAAgB,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM;AAEhE,QAAI,aAAa,SAAS,kBAAkB,OAAO,OAAO,SAAS,SAAS;AACxE,aAAO;AAAA,IACX;AACA,QAAI,+BAA+B,wBAAwB,eAAe,MAAM;AAChF,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,8BAA8B;AAC9B,oBAAc;AAAA,IAClB,OACK;AACD,oBAAc,kBAAkB,MAAM,MAAM;AAC5C,qCAA+B,wBAAwB,eAAe,MAAM;AAAA,IAEhF;AACA,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,CAAC,WAAW,WAAW,oBAAoB,UAAU,UAAU,SAAS;AACxE,iBAAW,UAAU;AAAA,IACzB;AACA,QAAI,CAAC,aAAa;AACd,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,WAAW,WAAW;AAC1C,WAAO,aAAa,eAAe,QAAQ,OAAO,kBAAkB,MAAM,SAAS,QAAQ,IAAI;AAAA,EACnG,GAAG,CAAC,CAAC;AACL,MAAI,gBAAsB,oBAAY,SAAU,QAAQ;AACpD,QAAI,QAAQ;AACZ,QAAI,CAAC,UAAU,UAAU,UAAU,UAAU,SAAS,CAAC,MAAMA,QAAO;AAEhE;AAAA,IACJ;AACA,QAAI,QAAQ,YAAY,QAAQ,WAAW,KAAK,IAAI,WAAW,KAAK;AACpE,QAAI,cAAc,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,aAAO,EAAE,SAAS,MAAM,SAAS,EAAE,WAAW,MAAM,UAAU,MAAM,WAAW,EAAE,iBAAiB,aAAa,EAAE,OAAO,KAAK;AAAA,IAAG,CAAC,EAAE,CAAC;AAEvM,QAAI,eAAe,YAAY,QAAQ;AACnC,UAAI,MAAM,YAAY;AAClB,cAAM,eAAe;AAAA,MACzB;AACA;AAAA,IACJ;AAEA,QAAI,CAAC,aAAa;AACd,UAAI,cAAc,UAAU,QAAQ,UAAU,CAAC,GAC1C,IAAI,UAAU,EACd,OAAO,OAAO,EACd,OAAO,SAAU,MAAM;AAAE,eAAO,KAAK,SAAS,MAAM,MAAM;AAAA,MAAG,CAAC;AACnE,UAAI,aAAa,WAAW,SAAS,IAAI,kBAAkB,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,QAAQ;AACtG,UAAI,YAAY;AACZ,YAAI,MAAM,YAAY;AAClB,gBAAM,eAAe;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,eAAqB,oBAAY,SAAU,MAAM,OAAO,QAAQ,QAAQ;AACxE,QAAI,QAAQ,EAAE,MAAY,OAAc,QAAgB,QAAgB,cAAc,yBAAyB,MAAM,EAAE;AACvH,uBAAmB,QAAQ,KAAK,KAAK;AACrC,eAAW,WAAY;AACnB,yBAAmB,UAAU,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,eAAO,MAAM;AAAA,MAAO,CAAC;AAAA,IACvG,GAAG,CAAC;AAAA,EACR,GAAG,CAAC,CAAC;AACL,MAAI,mBAAyB,oBAAY,SAAU,OAAO;AACtD,kBAAc,UAAU,WAAW,KAAK;AACxC,eAAW,UAAU;AAAA,EACzB,GAAG,CAAC,CAAC;AACL,MAAI,cAAoB,oBAAY,SAAU,OAAO;AACjD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,MAAI,kBAAwB,oBAAY,SAAU,OAAO;AACrD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,EAAM,kBAAU,WAAY;AACxB,cAAU,KAAKA,MAAK;AACpB,UAAM,aAAa;AAAA,MACf,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACxB,CAAC;AACD,aAAS,iBAAiB,SAAS,eAAe,UAAU;AAC5D,aAAS,iBAAiB,aAAa,eAAe,UAAU;AAChE,aAAS,iBAAiB,cAAc,kBAAkB,UAAU;AACpE,WAAO,WAAY;AACf,kBAAY,UAAU,OAAO,SAAU,MAAM;AAAE,eAAO,SAASA;AAAA,MAAO,CAAC;AACvE,eAAS,oBAAoB,SAAS,eAAe,UAAU;AAC/D,eAAS,oBAAoB,aAAa,eAAe,UAAU;AACnE,eAAS,oBAAoB,cAAc,kBAAkB,UAAU;AAAA,IAC3E;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,kBAAkB,MAAM,iBAAiB,QAAQ,MAAM;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,QAAc,sBAAcA,QAAO,EAAE,QAAQ,cAAc,EAAE,EAAE,CAAC,IAAI;AAAA,IACpE,kBAAwB,sBAAc,iBAAiB,EAAE,YAAY,MAAM,YAAY,SAAS,MAAM,QAAQ,CAAC,IAAI;AAAA,EAAI;AAC/H;AACA,SAAS,yBAAyB,MAAM;AACpC,MAAI,eAAe;AACnB,SAAO,SAAS,MAAM;AAClB,QAAI,gBAAgB,YAAY;AAC5B,qBAAe,KAAK;AACpB,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,KAAK;AAAA,EAChB;AACA,SAAO;AACX;;;ASzJA,IAAO,kBAAQ,cAAc,WAAW,mBAAmB;;;ArBC3D,IAAI,oBAA0B,mBAAW,SAAU,OAAO,KAAK;AAAE,SAAc,sBAAc,cAAc,SAAS,CAAC,GAAG,OAAO,EAAE,KAAU,SAAS,gBAAQ,CAAC,CAAC;AAAI,CAAC;AACnK,kBAAkB,aAAa,aAAa;AAC5C,IAAO,sBAAQ;;;AhBkML,IAAAC,sBAAA;AA3KV,IAAM,YAAY,CAAC,KAAK,SAAS,WAAW,WAAW;AACvD,IAAM,iBAAiB,CAAC,KAAK,OAAO;AAMpC,IAAM,cAAc;AAGpB,IAAM,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAGzD,WAAW;AAGb,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,aAAa;EAC/E;EACA;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AAoBzC,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAQ9F,IAAM,CAAC,6BAA6B,6BAA6B,IAC/D,oBAAqD,WAAW;AAoDlE,IAAM,SAAgC,CAAC,UAAoC;AACzE,QAAM;IACJ;IACA;IACA,MAAM;IACN;IACA;IACA,OAAO;IACP;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AACJ,QAAM,cAAc,eAAe,aAAa;AAChD,QAAM,CAAC,SAAS,UAAU,IAAU,iBAAsC,IAAI;AAC9E,QAAM,CAAC,WAAW,YAAY,IAAU,iBAAoC,IAAI;AAChF,QAAM,CAAC,sBAAsB,uBAAuB,IAAU,iBAAS,KAAK;AAC5E,QAAM,YAAY,aAAa,GAAG;AAClC,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;IAC3C,MAAM;IACN,aAAa,eAAe;IAC5B,UAAU;IACV,QAAQ;EACV,CAAC;AACD,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;IAC7C,MAAM;IACN,aAAa;IACb,UAAU;IACV,QAAQ;EACV,CAAC;AACD,QAAM,2BAAiC,eAAwC,IAAI;AAGnF,QAAM,gBAAgB,UAAU,QAAQ,CAAC,CAAC,QAAQ,QAAQ,MAAM,IAAI;AACpE,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,iBAAS,oBAAI,IAAkB,CAAC;AAOtF,QAAM,kBAAkB,MAAM,KAAK,gBAAgB,EAChD,IAAI,CAAC,WAAW,OAAO,MAAM,KAAK,EAClC,KAAK,GAAG;AAEX,aACE,yBAAiB,OAAhB,EAAsB,GAAG,aACxB,cAAA;IAAC;IAAA;MACC;MACA,OAAO;MACP;MACA,iBAAiB;MACjB;MACA,mBAAmB;MACnB;MACA,8BAA8B;MAC9B,WAAW,MAAM;MACjB;MACA,eAAe;MACf;MACA,cAAc;MACd,KAAK;MACL;MACA;MAEA,UAAA;YAAA,yBAAC,WAAW,UAAX,EAAoB,OAAO,eAC1B,cAAA;UAAC;UAAA;YACC,OAAO,MAAM;YACb,mBAAyB,oBAAY,CAAC,WAAW;AAC/C,kCAAoB,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC;YACzD,GAAG,CAAC,CAAC;YACL,sBAA4B,oBAAY,CAAC,WAAW;AAClD,kCAAoB,CAAC,SAAS;AAC5B,sBAAM,aAAa,IAAI,IAAI,IAAI;AAC/B,2BAAW,OAAO,MAAM;AACxB,uBAAO;cACT,CAAC;YACH,GAAG,CAAC,CAAC;YAEJ;UAAA;QACH,EAAA,CACF;QAEC,oBACC;UAAC;UAAA;YAEC,eAAW;YACX;YACA,UAAU;YACV;YACA;YACA;YAEA,UAAU,CAAC,UAAU,SAAS,MAAM,OAAO,KAAK;YAChD;YACA;YAEC,UAAA;cAAA,UAAU,aAAY,yBAAC,UAAA,EAAO,OAAM,GAAA,CAAG,IAAK;cAC5C,MAAM,KAAK,gBAAgB;YAAA;UAAA;UAbvB;QAcP,IACE;MAAA;IAAA;EACN,EAAA,CACF;AAEJ;AAEA,OAAO,cAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,eAAe,WAAW,OAAO,GAAG,aAAa,IAAI;AAC7D,UAAM,cAAc,eAAe,aAAa;AAChD,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,aAAa,QAAQ,YAAY;AACvC,UAAM,eAAe,gBAAgB,cAAc,QAAQ,eAAe;AAC1E,UAAM,WAAW,cAAc,aAAa;AAC5C,UAAM,iBAAuB,eAA0C,OAAO;AAE9E,UAAM,CAAC,WAAW,uBAAuB,cAAc,IAAI,mBAAmB,CAAC,WAAW;AACxF,YAAM,eAAe,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AAC/D,YAAM,cAAc,aAAa,KAAK,CAAC,SAAS,KAAK,UAAU,QAAQ,KAAK;AAC5E,YAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;AAC/D,UAAI,aAAa,QAAW;AAC1B,gBAAQ,cAAc,SAAS,KAAK;MACtC;IACF,CAAC;AAED,UAAM,aAAa,CAAC,iBAAyD;AAC3E,UAAI,CAAC,YAAY;AACf,gBAAQ,aAAa,IAAI;AAEzB,uBAAe;MACjB;AAEA,UAAI,cAAc;AAChB,gBAAQ,yBAAyB,UAAU;UACzC,GAAG,KAAK,MAAM,aAAa,KAAK;UAChC,GAAG,KAAK,MAAM,aAAa,KAAK;QAClC;MACF;IACF;AAEA,eACE,yBAAiB,QAAhB,EAAuB,SAAO,MAAE,GAAG,aAClC,cAAA;MAAC,UAAU;MAAV;QACC,MAAK;QACL,MAAK;QACL,iBAAe,QAAQ;QACvB,iBAAe,QAAQ;QACvB,iBAAe,QAAQ;QACvB,qBAAkB;QAClB,KAAK,QAAQ;QACb,cAAY,QAAQ,OAAO,SAAS;QACpC,UAAU;QACV,iBAAe,aAAa,KAAK;QACjC,oBAAkB,sBAAsB,QAAQ,KAAK,IAAI,KAAK;QAC7D,GAAG;QACJ,KAAK;QAEL,SAAS,qBAAqB,aAAa,SAAS,CAAC,UAAU;AAM7D,gBAAM,cAAc,MAAM;AAG1B,cAAI,eAAe,YAAY,SAAS;AACtC,uBAAW,KAAK;UAClB;QACF,CAAC;QACD,eAAe,qBAAqB,aAAa,eAAe,CAAC,UAAU;AACzE,yBAAe,UAAU,MAAM;AAI/B,gBAAM,SAAS,MAAM;AACrB,cAAI,OAAO,kBAAkB,MAAM,SAAS,GAAG;AAC7C,mBAAO,sBAAsB,MAAM,SAAS;UAC9C;AAKA,cAAI,MAAM,WAAW,KAAK,MAAM,YAAY,SAAS,MAAM,gBAAgB,SAAS;AAClF,uBAAW,KAAK;AAEhB,kBAAM,eAAe;UACvB;QACF,CAAC;QACD,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AACjE,gBAAM,gBAAgB,UAAU,YAAY;AAC5C,gBAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAC7D,cAAI,CAAC,iBAAiB,MAAM,IAAI,WAAW,EAAG,uBAAsB,MAAM,GAAG;AAC7E,cAAI,iBAAiB,MAAM,QAAQ,IAAK;AACxC,cAAI,UAAU,SAAS,MAAM,GAAG,GAAG;AACjC,uBAAW;AACX,kBAAM,eAAe;UACvB;QACF,CAAC;MAAA;IACH,EAAA,CACF;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,aAAa;AAQnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AAEtD,UAAM,EAAE,eAAe,WAAW,OAAO,UAAU,cAAc,IAAI,GAAG,WAAW,IAAI;AACvF,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,EAAE,6BAA6B,IAAI;AACzC,UAAM,cAAc,aAAa;AACjC,UAAM,eAAe,gBAAgB,cAAc,QAAQ,iBAAiB;AAE5E,qBAAgB,MAAM;AACpB,mCAA6B,WAAW;IAC1C,GAAG,CAAC,8BAA8B,WAAW,CAAC;AAE9C,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QAGL,OAAO,EAAE,eAAe,OAAO;QAE9B,UAAA,sBAAsB,QAAQ,KAAK,QAAI,yBAAA,8BAAA,EAAG,UAAA,YAAA,CAAY,IAAM;MAAA;IAC/D;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,YAAY;AAKlB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,eAAe,UAAU,GAAG,UAAU,IAAI;AAClD,eACE,yBAAC,UAAU,MAAV,EAAe,eAAW,MAAE,GAAG,WAAW,KAAK,cAC7C,UAAA,YAAY,IAAA,CACf;EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAMC,eAAc;AAWpB,IAAM,eAA4C,CAAC,UAA0C;AAC3F,aAAO,yBAAC,QAAA,EAAgB,SAAO,MAAE,GAAG,MAAA,CAAO;AAC7C;AAEA,aAAa,cAAcA;AAM3B,IAAMC,gBAAe;AAKrB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,UAAU,iBAAiBA,eAAc,MAAM,aAAa;AAClE,UAAM,CAAC,UAAU,WAAW,IAAU,iBAA2B;AAGjE,qBAAgB,MAAM;AACpB,kBAAY,IAAI,iBAAiB,CAAC;IACpC,GAAG,CAAC,CAAC;AAEL,QAAI,CAAC,QAAQ,MAAM;AACjB,YAAM,OAAO;AACb,aAAO,OACM;YACP,yBAAC,uBAAA,EAAsB,OAAO,MAAM,eAClC,cAAA,yBAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,eAC5B,cAAA,yBAAC,OAAA,EAAK,UAAA,MAAM,SAAA,CAAS,EAAA,CACvB,EAAA,CACF;QACA;MACF,IACA;IACN;AAEA,eAAO,yBAAC,mBAAA,EAAmB,GAAG,OAAO,KAAK,aAAA,CAAc;EAC1D;AACF;AAEA,cAAc,cAAcA;AAM5B,IAAM,iBAAiB;AAqBvB,IAAM,CAAC,uBAAuB,uBAAuB,IACnD,oBAA+CA,aAAY;AAE7D,IAAM,oBAAoB;AA8B1B,IAAM,OAAO,WAAW,4BAA4B;AAEpD,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM;MACJ;MACA,WAAW;MACX;MACA;MACA;;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,iBAAiBA,eAAc,aAAa;AAC5D,UAAM,CAAC,SAAS,UAAU,IAAU,iBAA0C,IAAI;AAClF,UAAM,CAAC,UAAU,WAAW,IAAU,iBAAuC,IAAI;AACjF,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAC7E,UAAM,CAAC,cAAc,eAAe,IAAU,iBAAmC,IAAI;AACrF,UAAM,CAAC,kBAAkB,mBAAmB,IAAU;MACpD;IACF;AACA,UAAM,WAAW,cAAc,aAAa;AAC5C,UAAM,CAAC,cAAc,eAAe,IAAU,iBAAS,KAAK;AAC5D,UAAM,yBAA+B,eAAO,KAAK;AAG3C,IAAA,kBAAU,MAAM;AACpB,UAAI,QAAS,QAAO,WAAW,OAAO;IACxC,GAAG,CAAC,OAAO,CAAC;AAIZ,mBAAe;AAEf,UAAMC,cAAmB;MACvB,CAAC,eAA0C;AACzC,cAAM,CAAC,WAAW,GAAG,SAAS,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI,OAAO;AAC3E,cAAM,CAAC,QAAQ,IAAI,UAAU,MAAM,EAAE;AAErC,cAAM,6BAA6B,SAAS;AAC5C,mBAAW,aAAa,YAAY;AAElC,cAAI,cAAc,2BAA4B;AAC9C,iDAAW,eAAe,EAAE,OAAO,UAAU;AAE7C,cAAI,cAAc,aAAa,SAAU,UAAS,YAAY;AAC9D,cAAI,cAAc,YAAY,SAAU,UAAS,YAAY,SAAS;AACtE,iDAAW;AACX,cAAI,SAAS,kBAAkB,2BAA4B;QAC7D;MACF;MACA,CAAC,UAAU,QAAQ;IACrB;AAEA,UAAM,oBAA0B;MAC9B,MAAMA,YAAW,CAAC,cAAc,OAAO,CAAC;MACxC,CAACA,aAAY,cAAc,OAAO;IACpC;AAIM,IAAA,kBAAU,MAAM;AACpB,UAAI,cAAc;AAChB,0BAAkB;MACpB;IACF,GAAG,CAAC,cAAc,iBAAiB,CAAC;AAIpC,UAAM,EAAE,cAAc,yBAAyB,IAAI;AAC7C,IAAA,kBAAU,MAAM;AACpB,UAAI,SAAS;AACX,YAAI,mBAAmB,EAAE,GAAG,GAAG,GAAG,EAAE;AAEpC,cAAM,oBAAoB,CAAC,UAAwB;;AACjD,6BAAmB;YACjB,GAAG,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,OAAK,8BAAyB,YAAzB,mBAAkC,MAAK,EAAE;YAChF,GAAG,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,OAAK,8BAAyB,YAAzB,mBAAkC,MAAK,EAAE;UAClF;QACF;AACA,cAAM,kBAAkB,CAAC,UAAwB;AAE/C,cAAI,iBAAiB,KAAK,MAAM,iBAAiB,KAAK,IAAI;AACxD,kBAAM,eAAe;UACvB,OAAO;AAEL,gBAAI,CAAC,QAAQ,SAAS,MAAM,MAAqB,GAAG;AAClD,2BAAa,KAAK;YACpB;UACF;AACA,mBAAS,oBAAoB,eAAe,iBAAiB;AAC7D,mCAAyB,UAAU;QACrC;AAEA,YAAI,yBAAyB,YAAY,MAAM;AAC7C,mBAAS,iBAAiB,eAAe,iBAAiB;AAC1D,mBAAS,iBAAiB,aAAa,iBAAiB,EAAE,SAAS,MAAM,MAAM,KAAK,CAAC;QACvF;AAEA,eAAO,MAAM;AACX,mBAAS,oBAAoB,eAAe,iBAAiB;AAC7D,mBAAS,oBAAoB,aAAa,iBAAiB,EAAE,SAAS,KAAK,CAAC;QAC9E;MACF;IACF,GAAG,CAAC,SAAS,cAAc,wBAAwB,CAAC;AAE9C,IAAA,kBAAU,MAAM;AACpB,YAAM,QAAQ,MAAM,aAAa,KAAK;AACtC,aAAO,iBAAiB,QAAQ,KAAK;AACrC,aAAO,iBAAiB,UAAU,KAAK;AACvC,aAAO,MAAM;AACX,eAAO,oBAAoB,QAAQ,KAAK;AACxC,eAAO,oBAAoB,UAAU,KAAK;MAC5C;IACF,GAAG,CAAC,YAAY,CAAC;AAEjB,UAAM,CAAC,WAAW,qBAAqB,IAAI,mBAAmB,CAAC,WAAW;AACxE,YAAM,eAAe,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AAC/D,YAAM,cAAc,aAAa,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,SAAS,aAAa;AAC3F,YAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;AAC/D,UAAI,UAAU;AAKZ,mBAAW,MAAO,SAAS,IAAI,QAAwB,MAAM,CAAC;MAChE;IACF,CAAC;AAED,UAAM,kBAAwB;MAC5B,CAAC,MAAgC,OAAe,aAAsB;AACpE,cAAM,mBAAmB,CAAC,uBAAuB,WAAW,CAAC;AAC7D,cAAM,iBAAiB,QAAQ,UAAU,UAAa,QAAQ,UAAU;AACxE,YAAI,kBAAkB,kBAAkB;AACtC,0BAAgB,IAAI;AACpB,cAAI,iBAAkB,wBAAuB,UAAU;QACzD;MACF;MACA,CAAC,QAAQ,KAAK;IAChB;AACA,UAAM,kBAAwB,oBAAY,MAAM,mCAAS,SAAS,CAAC,OAAO,CAAC;AAC3E,UAAM,sBAA4B;MAChC,CAAC,MAAoC,OAAe,aAAsB;AACxE,cAAM,mBAAmB,CAAC,uBAAuB,WAAW,CAAC;AAC7D,cAAM,iBAAiB,QAAQ,UAAU,UAAa,QAAQ,UAAU;AACxE,YAAI,kBAAkB,kBAAkB;AACtC,8BAAoB,IAAI;QAC1B;MACF;MACA,CAAC,QAAQ,KAAK;IAChB;AAEA,UAAM,iBAAiB,aAAa,WAAW,uBAAuB;AAGtE,UAAM,qBACJ,mBAAmB,uBACf;MACE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,IACA,CAAC;AAEP,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA,kBAAkB;QAClB;QACA;QACA,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QAEA,cAAA,yBAAC,qBAAA,EAAa,IAAI,MAAM,gBAAc,MACpC,cAAA;UAAC;UAAA;YACC,SAAO;YAGP,SAAS,QAAQ;YACjB,kBAAkB,CAAC,UAAU;AAE3B,oBAAM,eAAe;YACvB;YACA,oBAAoB,qBAAqB,kBAAkB,CAAC,UAAU;;AACpE,4BAAQ,YAAR,mBAAiB,MAAM,EAAE,eAAe,KAAK;AAC7C,oBAAM,eAAe;YACvB,CAAC;YAED,cAAA;cAAC;cAAA;gBACC,SAAO;gBACP,6BAA2B;gBAC3B;gBACA;gBAGA,gBAAgB,CAAC,UAAU,MAAM,eAAe;gBAChD,WAAW,MAAM,QAAQ,aAAa,KAAK;gBAE3C,cAAA;kBAAC;kBAAA;oBACC,MAAK;oBACL,IAAI,QAAQ;oBACZ,cAAY,QAAQ,OAAO,SAAS;oBACpC,KAAK,QAAQ;oBACb,eAAe,CAAC,UAAU,MAAM,eAAe;oBAC9C,GAAG;oBACH,GAAG;oBACJ,UAAU,MAAM,gBAAgB,IAAI;oBACpC,KAAK;oBACL,OAAO;;sBAEL,SAAS;sBACT,eAAe;;sBAEf,SAAS;sBACT,GAAG,aAAa;oBAClB;oBACA,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AACjE,4BAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAG7D,0BAAI,MAAM,QAAQ,MAAO,OAAM,eAAe;AAE9C,0BAAI,CAAC,iBAAiB,MAAM,IAAI,WAAW,EAAG,uBAAsB,MAAM,GAAG;AAE7E,0BAAI,CAAC,WAAW,aAAa,QAAQ,KAAK,EAAE,SAAS,MAAM,GAAG,GAAG;AAC/D,8BAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,4BAAI,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AAE1D,4BAAI,CAAC,WAAW,KAAK,EAAE,SAAS,MAAM,GAAG,GAAG;AAC1C,2CAAiB,eAAe,MAAM,EAAE,QAAQ;wBAClD;AACA,4BAAI,CAAC,WAAW,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AAChD,gCAAM,iBAAiB,MAAM;AAC7B,gCAAM,eAAe,eAAe,QAAQ,cAAc;AAC1D,2CAAiB,eAAe,MAAM,eAAe,CAAC;wBACxD;AAMA,mCAAW,MAAMA,YAAW,cAAc,CAAC;AAE3C,8BAAM,eAAe;sBACvB;oBACF,CAAC;kBAAA;gBACH;cAAA;YACF;UAAA;QACF,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,6BAA6B;AAKnC,IAAM,4BAAkC,mBAGtC,CAAC,OAAoD,iBAAiB;AACtE,QAAM,EAAE,eAAe,UAAU,GAAG,YAAY,IAAI;AACpD,QAAM,UAAU,iBAAiBD,eAAc,aAAa;AAC5D,QAAM,iBAAiB,wBAAwBA,eAAc,aAAa;AAC1E,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,iBAAgC,IAAI;AACtF,QAAM,CAAC,SAAS,UAAU,IAAU,iBAAkD,IAAI;AAC1F,QAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAC7E,QAAM,WAAW,cAAc,aAAa;AAC5C,QAAM,0BAAgC,eAAO,KAAK;AAClD,QAAM,sBAA4B,eAAO,IAAI;AAE7C,QAAM,EAAE,UAAU,cAAc,kBAAkB,kBAAkB,IAAI;AACxE,QAAM,WAAiB,oBAAY,MAAM;AACvC,QACE,QAAQ,WACR,QAAQ,aACR,kBACA,WACA,YACA,gBACA,kBACA;AACA,YAAM,cAAc,QAAQ,QAAQ,sBAAsB;AAK1D,YAAM,cAAc,QAAQ,sBAAsB;AAClD,YAAM,gBAAgB,QAAQ,UAAU,sBAAsB;AAC9D,YAAM,eAAe,iBAAiB,sBAAsB;AAE5D,UAAI,QAAQ,QAAQ,OAAO;AACzB,cAAM,iBAAiB,aAAa,OAAO,YAAY;AACvD,cAAM,OAAO,cAAc,OAAO;AAClC,cAAM,YAAY,YAAY,OAAO;AACrC,cAAM,kBAAkB,YAAY,QAAQ;AAC5C,cAAM,eAAe,KAAK,IAAI,iBAAiB,YAAY,KAAK;AAChE,cAAM,YAAY,OAAO,aAAa;AACtC,cAAM,cAAc,MAAM,MAAM;UAC9B;;;;;;UAMA,KAAK,IAAI,gBAAgB,YAAY,YAAY;QACnD,CAAC;AAED,uBAAe,MAAM,WAAW,kBAAkB;AAClD,uBAAe,MAAM,OAAO,cAAc;MAC5C,OAAO;AACL,cAAM,iBAAiB,YAAY,QAAQ,aAAa;AACxD,cAAM,QAAQ,OAAO,aAAa,cAAc,QAAQ;AACxD,cAAM,aAAa,OAAO,aAAa,YAAY,QAAQ;AAC3D,cAAM,kBAAkB,YAAY,QAAQ;AAC5C,cAAM,eAAe,KAAK,IAAI,iBAAiB,YAAY,KAAK;AAChE,cAAM,WAAW,OAAO,aAAa;AACrC,cAAM,eAAe,MAAM,OAAO;UAChC;UACA,KAAK,IAAI,gBAAgB,WAAW,YAAY;QAClD,CAAC;AAED,uBAAe,MAAM,WAAW,kBAAkB;AAClD,uBAAe,MAAM,QAAQ,eAAe;MAC9C;AAKA,YAAM,QAAQ,SAAS;AACvB,YAAM,kBAAkB,OAAO,cAAc,iBAAiB;AAC9D,YAAM,cAAc,SAAS;AAE7B,YAAM,gBAAgB,OAAO,iBAAiB,OAAO;AACrD,YAAM,wBAAwB,SAAS,cAAc,gBAAgB,EAAE;AACvE,YAAM,oBAAoB,SAAS,cAAc,YAAY,EAAE;AAC/D,YAAM,2BAA2B,SAAS,cAAc,mBAAmB,EAAE;AAC7E,YAAM,uBAAuB,SAAS,cAAc,eAAe,EAAE;AACrE,YAAM,oBAAoB,wBAAwB,oBAAoB,cAAc,uBAAuB;AAC3G,YAAM,mBAAmB,KAAK,IAAI,aAAa,eAAe,GAAG,iBAAiB;AAElF,YAAM,iBAAiB,OAAO,iBAAiB,QAAQ;AACvD,YAAM,qBAAqB,SAAS,eAAe,YAAY,EAAE;AACjE,YAAM,wBAAwB,SAAS,eAAe,eAAe,EAAE;AAEvE,YAAM,yBAAyB,YAAY,MAAM,YAAY,SAAS,IAAI;AAC1E,YAAM,4BAA4B,kBAAkB;AAEpD,YAAM,yBAAyB,aAAa,eAAe;AAC3D,YAAM,mBAAmB,aAAa,YAAY;AAClD,YAAM,yBAAyB,wBAAwB,oBAAoB;AAC3E,YAAM,4BAA4B,oBAAoB;AAEtD,YAAM,8BAA8B,0BAA0B;AAE9D,UAAI,6BAA6B;AAC/B,cAAM,aACJ,MAAM,SAAS,KAAK,iBAAiB,MAAM,MAAM,SAAS,CAAC,EAAG,IAAI;AACpE,uBAAe,MAAM,SAAS;AAC9B,cAAM,uBACJ,QAAQ,eAAe,SAAS,YAAY,SAAS;AACvD,cAAM,mCAAmC,KAAK;UAC5C;UACA;WAEG,aAAa,wBAAwB,KACtC,uBACA;QACJ;AACA,cAAM,SAAS,yBAAyB;AACxC,uBAAe,MAAM,SAAS,SAAS;MACzC,OAAO;AACL,cAAM,cAAc,MAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC,EAAG,IAAI;AACvE,uBAAe,MAAM,MAAM;AAC3B,cAAM,gCAAgC,KAAK;UACzC;UACA,wBACE,SAAS;WAER,cAAc,qBAAqB,KACpC;QACJ;AACA,cAAM,SAAS,gCAAgC;AAC/C,uBAAe,MAAM,SAAS,SAAS;AACvC,iBAAS,YAAY,yBAAyB,yBAAyB,SAAS;MAClF;AAEA,qBAAe,MAAM,SAAS,GAAG,cAAc;AAC/C,qBAAe,MAAM,YAAY,mBAAmB;AACpD,qBAAe,MAAM,YAAY,kBAAkB;AAGnD;AAIA,4BAAsB,MAAO,wBAAwB,UAAU,IAAK;IACtE;EACF,GAAG;IACD;IACA,QAAQ;IACR,QAAQ;IACR;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;EACF,CAAC;AAED,mBAAgB,MAAM,SAAS,GAAG,CAAC,QAAQ,CAAC;AAG5C,QAAM,CAAC,eAAe,gBAAgB,IAAU,iBAAiB;AACjE,mBAAgB,MAAM;AACpB,QAAI,QAAS,kBAAiB,OAAO,iBAAiB,OAAO,EAAE,MAAM;EACvE,GAAG,CAAC,OAAO,CAAC;AAMZ,QAAM,2BAAiC;IACrC,CAAC,SAA+C;AAC9C,UAAI,QAAQ,oBAAoB,YAAY,MAAM;AAChD,iBAAS;AACT;AACA,4BAAoB,UAAU;MAChC;IACF;IACA,CAAC,UAAU,iBAAiB;EAC9B;AAEA,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA;MACA,sBAAsB;MAEtB,cAAA;QAAC;QAAA;UACC,KAAK;UACL,OAAO;YACL,SAAS;YACT,eAAe;YACf,UAAU;YACV,QAAQ;UACV;UAEA,cAAA;YAAC,UAAU;YAAV;cACE,GAAG;cACJ,KAAK;cACL,OAAO;;;gBAGL,WAAW;;gBAEX,WAAW;gBACX,GAAG,YAAY;cACjB;YAAA;UACF;QAAA;MACF;IAAA;EACF;AAEJ,CAAC;AAED,0BAA0B,cAAc;AAMxC,IAAM,uBAAuB;AAM7B,IAAM,uBAA6B,mBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM;IACJ;IACA,QAAQ;IACR,mBAAmB;IACnB,GAAG;EACL,IAAI;AACJ,QAAM,cAAc,eAAe,aAAa;AAEhD,aACE;IAAiB;IAAhB;MACE,GAAG;MACH,GAAG;MACJ,KAAK;MACL;MACA;MACA,OAAO;;QAEL,WAAW;QACX,GAAG,YAAY;;QAEf,GAAG;UACD,2CAA2C;UAC3C,0CAA0C;UAC1C,2CAA2C;UAC3C,gCAAgC;UAChC,iCAAiC;QACnC;MACF;IAAA;EACF;AAEJ,CAAC;AAED,qBAAqB,cAAc;AAYnC,IAAM,CAAC,wBAAwB,wBAAwB,IACrD,oBAAgDA,eAAc,CAAC,CAAC;AAElE,IAAM,gBAAgB;AAQtB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,eAAe,OAAO,GAAG,cAAc,IAAI;AACnD,UAAM,iBAAiB,wBAAwB,eAAe,aAAa;AAC3E,UAAM,kBAAkB,yBAAyB,eAAe,aAAa;AAC7E,UAAM,eAAe,gBAAgB,cAAc,eAAe,gBAAgB;AAClF,UAAM,mBAAyB,eAAO,CAAC;AACvC,eACE,0BAAA,8BAAA,EAEE,UAAA;UAAA;QAAC;QAAA;UACC,yBAAyB;YACvB,QAAQ;UACV;UACA;QAAA;MACF;UACA,yBAAC,WAAW,MAAX,EAAgB,OAAO,eACtB,cAAA;QAAC,UAAU;QAAV;UACC,8BAA2B;UAC3B,MAAK;UACJ,GAAG;UACJ,KAAK;UACL,OAAO;;;;YAIL,UAAU;YACV,MAAM;;;;;YAKN,UAAU;YACV,GAAG,cAAc;UACnB;UACA,UAAU,qBAAqB,cAAc,UAAU,CAAC,UAAU;AAChE,kBAAM,WAAW,MAAM;AACvB,kBAAM,EAAE,gBAAgB,wBAAwB,IAAI;AACpD,iBAAI,mEAAyB,YAAW,gBAAgB;AACtD,oBAAM,aAAa,KAAK,IAAI,iBAAiB,UAAU,SAAS,SAAS;AACzE,kBAAI,aAAa,GAAG;AAClB,sBAAM,kBAAkB,OAAO,cAAc,iBAAiB;AAC9D,sBAAM,eAAe,WAAW,eAAe,MAAM,SAAS;AAC9D,sBAAM,YAAY,WAAW,eAAe,MAAM,MAAM;AACxD,sBAAM,aAAa,KAAK,IAAI,cAAc,SAAS;AAEnD,oBAAI,aAAa,iBAAiB;AAChC,wBAAM,aAAa,aAAa;AAChC,wBAAM,oBAAoB,KAAK,IAAI,iBAAiB,UAAU;AAC9D,wBAAM,aAAa,aAAa;AAEhC,iCAAe,MAAM,SAAS,oBAAoB;AAClD,sBAAI,eAAe,MAAM,WAAW,OAAO;AACzC,6BAAS,YAAY,aAAa,IAAI,aAAa;AAEnD,mCAAe,MAAM,iBAAiB;kBACxC;gBACF;cACF;YACF;AACA,6BAAiB,UAAU,SAAS;UACtC,CAAC;QAAA;MACH,EAAA,CACF;IAAA,EAAA,CACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,aAAa;AAInB,IAAM,CAAC,4BAA4B,qBAAqB,IACtD,oBAA6C,UAAU;AAKzD,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,MAAM;AACtB,eACE,yBAAC,4BAAA,EAA2B,OAAO,eAAe,IAAI,SACpD,cAAA,yBAAC,UAAU,KAAV,EAAc,MAAK,SAAQ,mBAAiB,SAAU,GAAG,YAAY,KAAK,aAAA,CAAc,EAAA,CAC3F;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,eAAe,sBAAsB,YAAY,aAAa;AACpE,eAAO,yBAAC,UAAU,KAAV,EAAc,IAAI,aAAa,IAAK,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,YAAY;AAUlB,IAAM,CAAC,2BAA2B,oBAAoB,IACpD,oBAA4C,SAAS;AASvD,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM;MACJ;MACA;MACA,WAAW;MACX,WAAW;MACX,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,iBAAiB,WAAW,aAAa;AACzD,UAAM,iBAAiB,wBAAwB,WAAW,aAAa;AACvE,UAAM,aAAa,QAAQ,UAAU;AACrC,UAAM,CAAC,WAAW,YAAY,IAAU,iBAAS,iBAAiB,EAAE;AACpE,UAAM,CAAC,WAAW,YAAY,IAAU,iBAAS,KAAK;AACtD,UAAM,eAAe;MAAgB;MAAc,CAAC,SAAA;;AAClD,oCAAe,oBAAf,wCAAiC,MAAM,OAAO;;IAChD;AACA,UAAM,SAAS,MAAM;AACrB,UAAM,iBAAuB,eAA0C,OAAO;AAE9E,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,UAAU;AACb,gBAAQ,cAAc,KAAK;AAC3B,gBAAQ,aAAa,KAAK;MAC5B;IACF;AAEA,QAAI,UAAU,IAAI;AAChB,YAAM,IAAI;QACR;MACF;IACF;AAEA,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA;QACA;QACA,kBAAwB,oBAAY,CAAC,SAAS;AAC5C,uBAAa,CAAC,kBAAkB,mBAAkB,6BAAM,gBAAe,IAAI,KAAK,CAAC;QACnF,GAAG,CAAC,CAAC;QAEL,cAAA;UAAC,WAAW;UAAX;YACC,OAAO;YACP;YACA;YACA;YAEA,cAAA;cAAC,UAAU;cAAV;gBACC,MAAK;gBACL,mBAAiB;gBACjB,oBAAkB,YAAY,KAAK;gBAEnC,iBAAe,cAAc;gBAC7B,cAAY,aAAa,YAAY;gBACrC,iBAAe,YAAY;gBAC3B,iBAAe,WAAW,KAAK;gBAC/B,UAAU,WAAW,SAAY;gBAChC,GAAG;gBACJ,KAAK;gBACL,SAAS,qBAAqB,UAAU,SAAS,MAAM,aAAa,IAAI,CAAC;gBACzE,QAAQ,qBAAqB,UAAU,QAAQ,MAAM,aAAa,KAAK,CAAC;gBACxE,SAAS,qBAAqB,UAAU,SAAS,MAAM;AAErD,sBAAI,eAAe,YAAY,QAAS,cAAa;gBACvD,CAAC;gBACD,aAAa,qBAAqB,UAAU,aAAa,MAAM;AAG7D,sBAAI,eAAe,YAAY,QAAS,cAAa;gBACvD,CAAC;gBACD,eAAe,qBAAqB,UAAU,eAAe,CAAC,UAAU;AACtE,iCAAe,UAAU,MAAM;gBACjC,CAAC;gBACD,eAAe,qBAAqB,UAAU,eAAe,CAAC,UAAU;;AAEtE,iCAAe,UAAU,MAAM;AAC/B,sBAAI,UAAU;AACZ,yCAAe,gBAAf;kBACF,WAAW,eAAe,YAAY,SAAS;AAG7C,0BAAM,cAAc,MAAM,EAAE,eAAe,KAAK,CAAC;kBACnD;gBACF,CAAC;gBACD,gBAAgB,qBAAqB,UAAU,gBAAgB,CAAC,UAAU;;AACxE,sBAAI,MAAM,kBAAkB,SAAS,eAAe;AAClD,yCAAe,gBAAf;kBACF;gBACF,CAAC;gBACD,WAAW,qBAAqB,UAAU,WAAW,CAAC,UAAU;;AAC9D,wBAAM,kBAAgB,oBAAe,cAAf,mBAA0B,aAAY;AAC5D,sBAAI,iBAAiB,MAAM,QAAQ,IAAK;AACxC,sBAAI,eAAe,SAAS,MAAM,GAAG,EAAG,cAAa;AAErD,sBAAI,MAAM,QAAQ,IAAK,OAAM,eAAe;gBAC9C,CAAC;cAAA;YACH;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,iBAAiB;AAKvB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AAEzD,UAAM,EAAE,eAAe,WAAW,OAAO,GAAG,cAAc,IAAI;AAC9D,UAAM,UAAU,iBAAiB,gBAAgB,aAAa;AAC9D,UAAM,iBAAiB,wBAAwB,gBAAgB,aAAa;AAC5E,UAAM,cAAc,qBAAqB,gBAAgB,aAAa;AACtE,UAAM,uBAAuB,8BAA8B,gBAAgB,aAAa;AACxF,UAAM,CAAC,cAAc,eAAe,IAAU,iBAAuC,IAAI;AACzF,UAAM,eAAe;MACnB;MACA,CAAC,SAAS,gBAAgB,IAAI;MAC9B,YAAY;MACZ,CAAC,SAAA;;AAAS,oCAAe,wBAAf,wCAAqC,MAAM,YAAY,OAAO,YAAY;;IACtF;AAEA,UAAM,cAAc,6CAAc;AAClC,UAAM,eAAqB;MACzB,UACE,yBAAC,UAAA,EAA+B,OAAO,YAAY,OAAO,UAAU,YAAY,UAC7E,UAAA,YAAA,GADU,YAAY,KAEzB;MAEF,CAAC,YAAY,UAAU,YAAY,OAAO,WAAW;IACvD;AAEA,UAAM,EAAE,mBAAmB,qBAAqB,IAAI;AACpD,qBAAgB,MAAM;AACpB,wBAAkB,YAAY;AAC9B,aAAO,MAAM,qBAAqB,YAAY;IAChD,GAAG,CAAC,mBAAmB,sBAAsB,YAAY,CAAC;AAE1D,eACE,0BAAA,8BAAA,EACE,UAAA;UAAA,yBAAC,UAAU,MAAV,EAAe,IAAI,YAAY,QAAS,GAAG,eAAe,KAAK,aAAA,CAAc;MAG7E,YAAY,cAAc,QAAQ,aAAa,CAAC,QAAQ,uBAC5C,uBAAa,cAAc,UAAU,QAAQ,SAAS,IAC/D;IAAA,EAAA,CACN;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,sBAAsB;AAK5B,IAAM,sBAA4B;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,eAAe,GAAG,mBAAmB,IAAI;AACjD,UAAM,cAAc,qBAAqB,qBAAqB,aAAa;AAC3E,WAAO,YAAY,iBACjB,yBAAC,UAAU,MAAV,EAAe,eAAW,MAAE,GAAG,oBAAoB,KAAK,aAAA,CAAc,IACrE;EACN;AACF;AAEA,oBAAoB,cAAc;AAMlC,IAAM,wBAAwB;AAK9B,IAAM,uBAA6B,mBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,iBAAiB,wBAAwB,uBAAuB,MAAM,aAAa;AACzF,QAAM,kBAAkB,yBAAyB,uBAAuB,MAAM,aAAa;AAC3F,QAAM,CAAC,aAAa,cAAc,IAAU,iBAAS,KAAK;AAC1D,QAAM,eAAe,gBAAgB,cAAc,gBAAgB,oBAAoB;AAEvF,mBAAgB,MAAM;AACpB,QAAI,eAAe,YAAY,eAAe,cAAc;AAE1D,UAASE,iBAAT,WAAwB;AACtB,cAAMC,eAAc,SAAS,YAAY;AACzC,uBAAeA,YAAW;MAC5B;AAHS,UAAAD,gBAAAA;AADT,YAAM,WAAW,eAAe;AAKhCA,MAAAA,eAAa;AACb,eAAS,iBAAiB,UAAUA,cAAY;AAChD,aAAO,MAAM,SAAS,oBAAoB,UAAUA,cAAY;IAClE;EACF,GAAG,CAAC,eAAe,UAAU,eAAe,YAAY,CAAC;AAEzD,SAAO,kBACL;IAAC;IAAA;MACE,GAAG;MACJ,KAAK;MACL,cAAc,MAAM;AAClB,cAAM,EAAE,UAAU,aAAa,IAAI;AACnC,YAAI,YAAY,cAAc;AAC5B,mBAAS,YAAY,SAAS,YAAY,aAAa;QACzD;MACF;IAAA;EACF,IACE;AACN,CAAC;AAED,qBAAqB,cAAc;AAMnC,IAAM,0BAA0B;AAKhC,IAAM,yBAA+B,mBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,iBAAiB,wBAAwB,yBAAyB,MAAM,aAAa;AAC3F,QAAM,kBAAkB,yBAAyB,yBAAyB,MAAM,aAAa;AAC7F,QAAM,CAAC,eAAe,gBAAgB,IAAU,iBAAS,KAAK;AAC9D,QAAM,eAAe,gBAAgB,cAAc,gBAAgB,oBAAoB;AAEvF,mBAAgB,MAAM;AACpB,QAAI,eAAe,YAAY,eAAe,cAAc;AAE1D,UAASA,iBAAT,WAAwB;AACtB,cAAM,YAAY,SAAS,eAAe,SAAS;AAGnD,cAAME,iBAAgB,KAAK,KAAK,SAAS,SAAS,IAAI;AACtD,yBAAiBA,cAAa;MAChC;AANS,UAAAF,gBAAAA;AADT,YAAM,WAAW,eAAe;AAQhCA,MAAAA,eAAa;AACb,eAAS,iBAAiB,UAAUA,cAAY;AAChD,aAAO,MAAM,SAAS,oBAAoB,UAAUA,cAAY;IAClE;EACF,GAAG,CAAC,eAAe,UAAU,eAAe,YAAY,CAAC;AAEzD,SAAO,oBACL;IAAC;IAAA;MACE,GAAG;MACJ,KAAK;MACL,cAAc,MAAM;AAClB,cAAM,EAAE,UAAU,aAAa,IAAI;AACnC,YAAI,YAAY,cAAc;AAC5B,mBAAS,YAAY,SAAS,YAAY,aAAa;QACzD;MACF;IAAA;EACF,IACE;AACN,CAAC;AAED,uBAAuB,cAAc;AAOrC,IAAM,yBAA+B,mBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,eAAe,cAAc,GAAG,qBAAqB,IAAI;AACjE,QAAM,iBAAiB,wBAAwB,sBAAsB,aAAa;AAClF,QAAM,qBAA2B,eAAsB,IAAI;AAC3D,QAAM,WAAW,cAAc,aAAa;AAE5C,QAAM,uBAA6B,oBAAY,MAAM;AACnD,QAAI,mBAAmB,YAAY,MAAM;AACvC,aAAO,cAAc,mBAAmB,OAAO;AAC/C,yBAAmB,UAAU;IAC/B;EACF,GAAG,CAAC,CAAC;AAEC,EAAA,kBAAU,MAAM;AACpB,WAAO,MAAM,qBAAqB;EACpC,GAAG,CAAC,oBAAoB,CAAC;AAMzB,mBAAgB,MAAM;;AACpB,UAAM,aAAa,SAAS,EAAE,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,SAAS,aAAa;AACxF,mDAAY,IAAI,YAAhB,mBAAyB,eAAe,EAAE,OAAO,UAAU;EAC7D,GAAG,CAAC,QAAQ,CAAC;AAEb,aACE;IAAC,UAAU;IAAV;MACC,eAAW;MACV,GAAG;MACJ,KAAK;MACL,OAAO,EAAE,YAAY,GAAG,GAAG,qBAAqB,MAAM;MACtD,eAAe,qBAAqB,qBAAqB,eAAe,MAAM;AAC5E,YAAI,mBAAmB,YAAY,MAAM;AACvC,6BAAmB,UAAU,OAAO,YAAY,cAAc,EAAE;QAClE;MACF,CAAC;MACD,eAAe,qBAAqB,qBAAqB,eAAe,MAAM;;AAC5E,6BAAe,gBAAf;AACA,YAAI,mBAAmB,YAAY,MAAM;AACvC,6BAAmB,UAAU,OAAO,YAAY,cAAc,EAAE;QAClE;MACF,CAAC;MACD,gBAAgB,qBAAqB,qBAAqB,gBAAgB,MAAM;AAC9E,6BAAqB;MACvB,CAAC;IAAA;EACH;AAEJ,CAAC;AAMD,IAAM,iBAAiB;AAKvB,IAAM,kBAAwB;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,eAAe,GAAG,eAAe,IAAI;AAC7C,eAAO,yBAAC,UAAU,KAAV,EAAc,eAAW,MAAE,GAAG,gBAAgB,KAAK,aAAA,CAAc;EAC3E;AACF;AAEA,gBAAgB,cAAc;AAM9B,IAAMG,cAAa;AAMnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,cAAc,eAAe,aAAa;AAChD,UAAM,UAAU,iBAAiBA,aAAY,aAAa;AAC1D,UAAM,iBAAiB,wBAAwBA,aAAY,aAAa;AACxE,WAAO,QAAQ,QAAQ,eAAe,aAAa,eACjD,yBAAiBC,QAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,aAAA,CAAc,IACzE;EACN;AACF;AAEA,YAAY,cAAcD;AAM1B,IAAM,oBAAoB;AAK1B,IAAM,oBAA0B;EAC9B,CAAC,EAAE,eAAe,OAAO,GAAG,MAAM,GAAwC,iBAAiB;AACzF,UAAM,MAAY,eAA0B,IAAI;AAChD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,YAAY,YAAY,KAAK;AAG7B,IAAA,kBAAU,MAAM;AACpB,YAAM,SAAS,IAAI;AACnB,UAAI,CAAC,OAAQ;AAEb,YAAM,cAAc,OAAO,kBAAkB;AAC7C,YAAM,aAAa,OAAO;QACxB;QACA;MACF;AACA,YAAM,WAAW,WAAW;AAC5B,UAAI,cAAc,SAAS,UAAU;AACnC,cAAM,QAAQ,IAAI,MAAM,UAAU,EAAE,SAAS,KAAK,CAAC;AACnD,iBAAS,KAAK,QAAQ,KAAK;AAC3B,eAAO,cAAc,KAAK;MAC5B;IACF,GAAG,CAAC,WAAW,KAAK,CAAC;AAcrB,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,OAAO,EAAE,GAAG,wBAAwB,GAAG,MAAM,MAAM;QACnD,KAAK;QACL,cAAc;MAAA;IAChB;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAIhC,SAAS,sBAAsB,OAAgB;AAC7C,SAAO,UAAU,MAAM,UAAU;AACnC;AAEA,SAAS,mBAAmB,gBAA0C;AACpE,QAAM,qBAAqB,eAAe,cAAc;AACxD,QAAM,YAAkB,eAAO,EAAE;AACjC,QAAM,WAAiB,eAAO,CAAC;AAE/B,QAAM,wBAA8B;IAClC,CAAC,QAAgB;AACf,YAAM,SAAS,UAAU,UAAU;AACnC,yBAAmB,MAAM;AAEzB,OAAC,SAAS,aAAa,OAAe;AACpC,kBAAU,UAAU;AACpB,eAAO,aAAa,SAAS,OAAO;AAEpC,YAAI,UAAU,GAAI,UAAS,UAAU,OAAO,WAAW,MAAM,aAAa,EAAE,GAAG,GAAI;MACrF,GAAG,MAAM;IACX;IACA,CAAC,kBAAkB;EACrB;AAEA,QAAM,iBAAuB,oBAAY,MAAM;AAC7C,cAAU,UAAU;AACpB,WAAO,aAAa,SAAS,OAAO;EACtC,GAAG,CAAC,CAAC;AAEC,EAAA,kBAAU,MAAM;AACpB,WAAO,MAAM,OAAO,aAAa,SAAS,OAAO;EACnD,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,WAAW,uBAAuB,cAAc;AAC1D;AAmBA,SAAS,aACP,OACA,QACA,aACA;AACA,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM,CAAC,SAAS,SAAS,OAAO,CAAC,CAAC;AAC7F,QAAM,mBAAmB,aAAa,OAAO,CAAC,IAAK;AACnD,QAAM,mBAAmB,cAAc,MAAM,QAAQ,WAAW,IAAI;AACpE,MAAI,eAAe,UAAU,OAAO,KAAK,IAAI,kBAAkB,CAAC,CAAC;AACjE,QAAM,qBAAqB,iBAAiB,WAAW;AACvD,MAAI,mBAAoB,gBAAe,aAAa,OAAO,CAAC,MAAM,MAAM,WAAW;AACnF,QAAM,WAAW,aAAa;IAAK,CAAC,SAClC,KAAK,UAAU,YAAY,EAAE,WAAW,iBAAiB,YAAY,CAAC;EACxE;AACA,SAAO,aAAa,cAAc,WAAW;AAC/C;AAMA,SAAS,UAAa,OAAY,YAAoB;AACpD,SAAO,MAAM,IAAO,CAAC,GAAGE,WAAU,OAAO,aAAaA,UAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAMC,SAAO;AACb,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,YAAY;AAClB,IAAMJ,UAAQ;", "names": ["React", "ReactDOM", "React", "node", "index", "handleAndDispatchPointerDownOutsideEvent", "React", "React", "import_jsx_runtime", "handleFocusIn", "handleFocusOut", "handleMutations", "container", "index", "React", "clamp", "platform", "max", "offset", "clamp", "platform", "placements", "sides", "side", "placement", "overflow", "platform", "x", "y", "min", "max", "clamp", "offset", "getComputedStyle", "getComputedStyle", "offset", "shift", "flip", "size", "hide", "arrow", "limitShift", "computePosition", "React", "noop", "platform", "computePosition", "data", "arrow", "offset", "shift", "limitShift", "flip", "size", "hide", "arrow", "React", "import_jsx_runtime", "import_jsx_runtime", "arrow", "offset", "shift", "limitShift", "flip", "size", "hide", "PopperArrow", "Root", "Arrow", "React", "import_react_dom", "import_jsx_runtime", "ReactDOM", "React", "import_jsx_runtime", "NAME", "__assign", "React", "React", "import_react", "useCallbackRef", "React", "useCallbackRef", "React", "import_react", "cbs", "React", "import_react", "React", "SideCar", "React", "React", "React", "Style", "import_jsx_runtime", "PORTAL_NAME", "CONTENT_NAME", "focusFirst", "handleScroll", "canScrollUp", "canScrollDown", "ARROW_NAME", "Arrow", "index", "Root", "Portal", "Content"]}