import OpenAI from 'openai';

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // 注意：在生产环境中应该通过后端API调用
});

/**
 * 生成绘本故事内容
 * @param {Object} params - 生成参数
 * @param {Object} params.character - 角色信息
 * @param {Object} params.story - 故事设置
 * @param {Object} params.content - 内容设置
 * @param {Function} params.onProgress - 进度回调函数
 * @returns {Promise<Object>} 生成的绘本内容
 */
export async function generatePictureBook({ character, story, content, onProgress }) {
  try {
    // 构建提示词
    const prompt = buildPrompt({ character, story, content });

    console.log('发送到OpenAI的提示词:', prompt);
    onProgress && onProgress('正在构建故事提示词...', 10);
    
    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "你是一个专业的儿童绘本创作者，擅长创作富有教育意义且适合儿童阅读的故事。请严格按照用户要求的格式返回JSON数据。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.8,
      max_tokens: 3000
    });

    const generatedContent = response.choices[0].message.content;
    console.log('OpenAI返回的原始内容:', generatedContent);
    onProgress && onProgress('故事内容生成完成，正在解析...', 50);
    
    // 解析返回的JSON
    let parsedContent;
    try {
      parsedContent = JSON.parse(generatedContent);
    } catch (parseError) {
      console.error('JSON解析错误:', parseError);
      // 如果JSON解析失败，尝试提取JSON部分
      const jsonMatch = generatedContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        parsedContent = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('无法解析OpenAI返回的内容');
      }
    }

    // 为每页生成插画
    console.log('开始生成插画...');
    onProgress && onProgress('正在生成插画...', 60);

    const pagesWithImages = await generateImagesForPages(
      parsedContent.pages,
      character,
      (current, total) => {
        const imageProgress = 60 + (current / total) * 35; // 60-95%
        onProgress && onProgress(`正在生成第${current}/${total}页插画...`, imageProgress);
      }
    );

    onProgress && onProgress('生成完成！', 100);

    return {
      ...parsedContent,
      pages: pagesWithImages
    };
    
  } catch (error) {
    console.error('OpenAI API调用失败:', error);
    
    // 如果API调用失败，返回默认内容
    return generateFallbackContent({ character, story, content });
  }
}

/**
 * 构建发送给OpenAI的提示词
 */
function buildPrompt({ character, story, content }) {
  const storyTypes = {
    'adventure': '冒险故事',
    'growth': '成长故事',
    'friendship': '友情故事',
    'life-skills': '生活技能'
  };

  const educationalTopic = content.isCustom 
    ? content.customContent 
    : content.randomTopic || '学会分享与合作';

  return `请为我创作一个儿童绘本故事，要求如下：

角色设定：
- 主角姓名：${character.name}
- 年龄：${character.age}岁
- 身份：${character.identity === 'human' ? '人类' : '动物'}
- 性别：${character.gender === 'any' ? '不限' : character.gender === 'boy' ? '男孩' : '女孩'}

故事设定：
- 故事类型：${storyTypes[story.type] || '成长故事'}
- 故事页数：${story.pages}页
- 教育主题：${educationalTopic}

请创作一个完整的绘本故事，包含以下内容：
1. 一个吸引人的故事标题
2. ${story.pages}页的故事内容，每页包含：
   - 页面标题
   - 故事文本（适合${character.age}岁儿童阅读，每页50-100字）
   - 场景描述（用于生成插图）

要求：
- 故事要体现"${educationalTopic}"这个教育主题
- 语言要适合${character.age}岁儿童理解
- 情节要积极向上，富有教育意义
- 每页的故事要连贯，有完整的故事弧线

请严格按照以下JSON格式返回：
{
  "title": "故事标题",
  "pages": [
    {
      "pageNumber": 1,
      "title": "第一页标题",
      "content": "第一页的故事内容...",
      "sceneDescription": "场景描述，用于生成插图"
    },
    {
      "pageNumber": 2,
      "title": "第二页标题", 
      "content": "第二页的故事内容...",
      "sceneDescription": "场景描述，用于生成插图"
    }
    // ... 继续到第${story.pages}页
  ],
  "educationalMessage": "这个故事传达的教育意义总结"
}`;
}

/**
 * 为绘本页面生成插画
 * @param {Array} pages - 绘本页面数组
 * @param {Object} character - 角色信息
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Array>} 包含插画的页面数组
 */
async function generateImagesForPages(pages, character, onProgress) {
  const pagesWithImages = [];

  for (let i = 0; i < pages.length; i++) {
    const page = pages[i];
    console.log(`正在为第${page.pageNumber}页生成插画...`);

    // 更新进度
    onProgress && onProgress(i + 1, pages.length);

    try {
      // 构建图像生成提示词
      const imagePrompt = buildImagePrompt(page, character);

      const imageResponse = await openai.images.generate({
        model: "dall-e-3",
        prompt: imagePrompt,
        size: "1024x1024",
        quality: "standard",
        n: 1,
      });

      const imageUrl = imageResponse.data[0].url;
      console.log(`第${page.pageNumber}页插画生成成功:`, imageUrl);

      // 注意：跳过URL验证以避免CORS问题
      // DALL-E 3的图像URL在浏览器中是可以直接访问的

      pagesWithImages.push({
        ...page,
        imageUrl: imageUrl,
        imagePrompt: imagePrompt,
        imageGenerated: true,
        imageGeneratedAt: new Date().toISOString()
      });

      // 添加延迟以避免API限制
      if (i < pages.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

    } catch (error) {
      console.error(`第${page.pageNumber}页插画生成失败:`, error);

      // 如果图像生成失败，使用默认emoji
      pagesWithImages.push({
        ...page,
        imageUrl: null,
        imagePrompt: null,
        fallbackEmoji: ['🌈', '🦋', '🌸', '🌺', '🍀', '⭐', '🌙', '☀️', '🌻', '🎈'][i % 10]
      });
    }
  }

  return pagesWithImages;
}

/**
 * 构建DALL-E 3图像生成提示词
 * @param {Object} page - 页面内容
 * @param {Object} character - 角色信息
 * @returns {string} 图像生成提示词
 */
function buildImagePrompt(page, character) {
  const characterDesc = character.identity === 'human'
    ? `a ${character.age}-year-old ${character.gender === 'boy' ? 'boy' : character.gender === 'girl' ? 'girl' : 'child'} named ${character.name}`
    : `a cute ${character.gender === 'boy' ? 'male' : character.gender === 'girl' ? 'female' : ''} animal character named ${character.name}`;

  const basePrompt = `Children's book illustration featuring ${characterDesc}. ${page.sceneDescription}. `;

  const stylePrompt = `Art style: soft, warm, child-friendly illustration with bright colors, simple shapes, and a gentle cartoon style. The image should be suitable for children aged ${character.age}. No text or words in the image. Safe and positive content only.`;

  return basePrompt + stylePrompt;
}

/**
 * 生成备用内容（当API调用失败时使用）
 */
function generateFallbackContent({ character, story, content }) {
  const educationalTopic = content.isCustom 
    ? content.customContent 
    : content.randomTopic || '学会分享与合作';

  const pages = [];
  
  for (let i = 1; i <= story.pages; i++) {
    pages.push({
      pageNumber: i,
      title: `第${i}页`,
      content: `这是${character.name}的故事第${i}页。在这一页中，${character.name}遇到了新的挑战和机会，学习了关于"${educationalTopic}"的重要道理...`,
      sceneDescription: `${character.name}在一个温馨的场景中，体现${educationalTopic}的主题`
    });
  }

  return {
    title: `${character.name}的${educationalTopic}之旅`,
    pages: pages,
    educationalMessage: `通过这个故事，孩子们可以学习到${educationalTopic}的重要性。`
  };
}
