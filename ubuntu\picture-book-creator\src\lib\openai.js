import OpenAI from 'openai';
import { generateAutismFriendlyPrompt, extractSceneInfo, generateCharacterDescription } from './autismFriendlyPrompts.js';
import { generateHybridPictureBook, getRecommendedStrategy, checkHybridGenerationAvailability } from './hybridImageGeneration.js';

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // 注意：在生产环境中应该通过后端API调用
});

/**
 * 生成绘本故事内容
 * @param {Object} params - 生成参数
 * @param {Object} params.character - 角色信息
 * @param {Object} params.story - 故事设置
 * @param {Object} params.content - 内容设置
 * @param {Function} params.onProgress - 进度回调函数
 * @returns {Promise<Object>} 生成的绘本内容
 */
export async function generatePictureBook({ character, story, content, onProgress }) {
  try {
    // 构建提示词
    const prompt = buildPrompt({ character, story, content });

    console.log('发送到OpenAI的提示词:', prompt);
    onProgress && onProgress('正在构建故事提示词...', 10);
    
    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "你是一个专为自闭症儿童创作绘本的专家。请根据以下要求生成一个适合3-7岁自闭症儿童阅读的绘本故事：\n\n- 使用简洁明了的句子，每页不超过2-3句；\n- 故事结构简单清晰，有明确的开始、发展和结尾；\n- 包含积极的情绪引导，如情绪表达、规则学习或社交技能；\n- 加入重复的句型与角色行为，便于理解和记忆；\n- 避免使用比喻、讽刺、复杂隐喻等表达；\n- 每页为插画师提供一段简短的英文插图描述；\n\n请严格按照用户要求的格式返回JSON数据。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.8,
      max_tokens: 3000
    });

    const generatedContent = response.choices[0].message.content;
    console.log('OpenAI返回的原始内容:', generatedContent);
    onProgress && onProgress('故事内容生成完成，正在解析...', 50);
    
    // 解析返回的JSON
    let parsedContent;
    try {
      parsedContent = JSON.parse(generatedContent);
    } catch (parseError) {
      console.error('JSON解析错误:', parseError);
      // 如果JSON解析失败，尝试提取JSON部分
      const jsonMatch = generatedContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        parsedContent = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('无法解析OpenAI返回的内容');
      }
    }

    // 为每页生成插画
    console.log('开始生成插画...');
    onProgress && onProgress('正在生成插画...', 60);

    // 检查可用的图像生成策略
    const strategy = getRecommendedStrategy();
    console.log('使用的图像生成策略:', strategy);

    let pagesWithImages;

    if (strategy === 'hybrid') {
      // 使用混合模式：DALL-E 3 + LiblibAI
      onProgress && onProgress('使用混合模式生成插画...', 60);

      const hybridResult = await generateHybridPictureBook(
        character,
        parsedContent.pages,
        (message, progress) => {
          const mappedProgress = 60 + (progress * 0.35); // 60-95%
          onProgress && onProgress(message, mappedProgress);
        }
      );

      // 将混合生成结果转换为标准格式
      pagesWithImages = parsedContent.pages.map((page, index) => {
        const illustration = hybridResult.illustrations[index];
        return {
          ...page,
          imageUrl: illustration?.imageUrl || null,
          imagePrompt: 'Generated using hybrid mode (DALL-E 3 + LiblibAI)',
          imageGenerated: illustration?.success || false,
          imageGeneratedAt: new Date().toISOString(),
          generationMethod: illustration?.method || 'failed'
        };
      });

    } else {
      // 使用传统模式：仅DALL-E 3
      onProgress && onProgress('使用DALL-E 3生成插画...', 60);

      pagesWithImages = await generateImagesForPages(
        parsedContent.pages,
        character,
        (current, total) => {
          const imageProgress = 60 + (current / total) * 35; // 60-95%
          onProgress && onProgress(`正在生成第${current}/${total}页插画...`, imageProgress);
        }
      );
    }

    onProgress && onProgress('生成完成！', 100);

    return {
      ...parsedContent,
      pages: pagesWithImages
    };
    
  } catch (error) {
    console.error('OpenAI API调用失败:', error);
    
    // 如果API调用失败，返回默认内容
    return generateFallbackContent({ character, story });
  }
}



/**
 * 构建发送给OpenAI的提示词
 */
function buildPrompt({ character, story, content }) {
  const storyTypes = {
    'adventure': '冒险故事',
    'growth': '成长故事',
    'friendship': '友情故事',
    'life-skills': '生活技能'
  };

  const educationalTopic = content.isCustom 
    ? content.customContent 
    : content.randomTopic || '学会分享与合作';

  // 标准化角色描述
  const characterDescription = generateCharacterDescription(character);
  const characterName = character.name || '主角';

  return `请为自闭症儿童创作一个绘本故事，要求如下：

角色设定：
- 主角：${characterName}
- 外貌：${characterDescription}
- 年龄：${character.age}岁

故事设定：
- 故事类型：${storyTypes[story.type] || '成长故事'}
- 故事页数：${story.pages}页
- 教育主题：${educationalTopic}

特殊要求（针对自闭症儿童）：
1. 每页文字不超过2-3句话，每句话不超过15个字
2. 使用简单、直接的表达，避免比喻和隐喻
3. 重复使用相同的句型结构，如"${characterName}看到了..."、"${characterName}感到..."
4. 故事要包含明确的情绪表达和社交技能学习
5. 每页场景描述必须用英文，便于生成插图

请创作一个完整的绘本故事，包含以下内容：
1. 一个简单明了的故事标题
2. ${story.pages}页的故事内容，每页包含：
   - 页面标题（简短明了）
   - 故事文本（2-3句话，语言简洁直白）
   - 英文场景描述（用于DALL-E 3生成插图）

要求：
- 故事要体现"${educationalTopic}"这个教育主题
- 语言要极其简单，适合自闭症儿童理解
- 情节要积极向上，有明确的行为示范
- 每页的故事要有重复的模式，便于记忆

请严格按照以下JSON格式返回：
{
  "title": "故事标题",
  "pages": [
    {
      "pageNumber": 1,
      "title": "第一页标题",
      "content": "第一页的故事内容（2-3句话）...",
      "sceneDescription": "English scene description for illustration generation"
    },
    {
      "pageNumber": 2,
      "title": "第二页标题",
      "content": "第二页的故事内容（2-3句话）...",
      "sceneDescription": "English scene description for illustration generation"
    }
    // ... 继续到第${story.pages}页
  ],
  "educationalMessage": "这个故事传达的教育意义总结"
}`;
}

/**
 * 为绘本页面生成插画
 * @param {Array} pages - 绘本页面数组
 * @param {Object} character - 角色信息
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Array>} 包含插画的页面数组
 */
async function generateImagesForPages(pages, character, onProgress) {
  const pagesWithImages = [];

  for (let i = 0; i < pages.length; i++) {
    const page = pages[i];
    console.log(`正在为第${page.pageNumber}页生成插画...`);

    // 更新进度
    onProgress && onProgress(i + 1, pages.length);

    try {
      // 构建图像生成提示词
      const imagePrompt = buildImagePrompt(page, character);

      const imageResponse = await openai.images.generate({
        model: "dall-e-3",
        prompt: imagePrompt,
        size: "1024x1024",
        quality: "standard",
        n: 1,
      });

      const imageUrl = imageResponse.data[0].url;
      console.log(`第${page.pageNumber}页插画生成成功:`, imageUrl);

      // 注意：跳过URL验证以避免CORS问题
      // DALL-E 3的图像URL在浏览器中是可以直接访问的

      pagesWithImages.push({
        ...page,
        imageUrl: imageUrl,
        imagePrompt: imagePrompt,
        imageGenerated: true,
        imageGeneratedAt: new Date().toISOString()
      });

      // 添加延迟以避免API限制
      if (i < pages.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

    } catch (error) {
      console.error(`第${page.pageNumber}页插画生成失败:`, error);

      // 如果图像生成失败，使用默认emoji
      pagesWithImages.push({
        ...page,
        imageUrl: null,
        imagePrompt: null,
        fallbackEmoji: ['🌈', '🦋', '🌸', '🌺', '🍀', '⭐', '🌙', '☀️', '🌻', '🎈'][i % 10]
      });
    }
  }

  return pagesWithImages;
}

/**
 * 构建DALL-E 3图像生成提示词
 * @param {Object} page - 页面内容
 * @param {Object} character - 角色信息
 * @returns {string} 图像生成提示词
 */
function buildImagePrompt(page, character) {
  // 从场景描述中提取情绪、动作和环境信息
  const sceneInfo = extractSceneInfo(page.sceneDescription || '');

  // 使用专业的自闭症友好关键词模块生成提示词
  const prompt = generateAutismFriendlyPrompt({
    character: character,
    sceneDescription: page.sceneDescription || 'A simple scene',
    emotion: sceneInfo.emotion,
    action: sceneInfo.action,
    environment: sceneInfo.environment
  });

  return prompt;
}



/**
 * 生成备用内容（当API调用失败时使用）
 */
function generateFallbackContent({ character, story }) {
  const characterName = character.name || '主角';
  const pages = [];

  // 为自闭症儿童设计的简单故事模板
  const simpleStoryTemplates = [
    {
      title: "认识自己",
      content: `${characterName}看着镜子。${characterName}很开心。`,
      sceneDescription: "character looking at mirror and smiling"
    },
    {
      title: "学会问好",
      content: `${characterName}见到朋友。${characterName}说："你好！"`,
      sceneDescription: "character waving hello to a friend"
    },
    {
      title: "分享玩具",
      content: `${characterName}有一个玩具。${characterName}和朋友一起玩。`,
      sceneDescription: "character sharing a toy with a friend"
    },
    {
      title: "表达感受",
      content: `${characterName}感到开心。${characterName}笑了。`,
      sceneDescription: "character expressing happiness with a big smile"
    }
  ];

  for (let i = 1; i <= story.pages; i++) {
    const template = simpleStoryTemplates[(i - 1) % simpleStoryTemplates.length];
    pages.push({
      pageNumber: i,
      title: template.title,
      content: template.content,
      sceneDescription: template.sceneDescription
    });
  }

  return {
    title: `${characterName}的成长故事`,
    pages: pages,
    educationalMessage: `通过这个简单的故事，孩子们可以学习基本的社交技能和情绪表达。`
  };
}
