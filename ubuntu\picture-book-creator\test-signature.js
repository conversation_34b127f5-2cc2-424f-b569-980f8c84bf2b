/**
 * 测试LiblibAI签名生成
 */

import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

// 测试数据
const testUri = '/api/generate/webui/text2img/ultra';
const testTimestamp = '1640995200000'; // 固定时间戳用于测试
const testNonce = 'test-nonce-12345'; // 固定随机字符串用于测试
const secretKey = process.env.VITE_LIBLIB_SECRET_KEY;

console.log('=== LiblibAI签名生成测试 ===');
console.log('URI:', testUri);
console.log('Timestamp:', testTimestamp);
console.log('Nonce:', testNonce);
console.log('SecretKey:', secretKey);

// 构建原文
const content = [testUri, testTimestamp, testNonce].join('&');
console.log('原文:', content);

// 方法1：标准base64编码
const digest1 = crypto.createHmac('sha1', secretKey)
  .update(content, 'utf8')
  .digest();

const signature1 = digest1.toString('base64').replace(/=+$/, '');
console.log('方法1 (标准base64):', signature1);

// 方法2：URL安全base64编码
const digest2 = crypto.createHmac('sha1', secretKey)
  .update(content, 'utf8')
  .digest();

const signature2 = digest2.toString('base64')
  .replace(/\+/g, '-')
  .replace(/\//g, '_')
  .replace(/=+$/, '');
console.log('方法2 (URL安全base64):', signature2);

// 方法3：使用Buffer的base64url编码（Node.js 14.18+）
try {
  const digest3 = crypto.createHmac('sha1', secretKey)
    .update(content, 'utf8')
    .digest();
  
  const signature3 = digest3.toString('base64url');
  console.log('方法3 (base64url):', signature3);
} catch (error) {
  console.log('方法3不支持 (Node.js版本过低)');
}

console.log('=== 测试完成 ===');
