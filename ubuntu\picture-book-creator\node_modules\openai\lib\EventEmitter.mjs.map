{"version": 3, "file": "EventEmitter.mjs", "sourceRoot": "", "sources": ["../src/lib/EventEmitter.ts"], "names": [], "mappings": ";;AAWA,MAAM,OAAO,YAAY;IAAzB;QACE,kCAEI,EAAE,EAAC;IAmFT,CAAC;IAjFC;;;;;;OAMG;IACH,EAAE,CAAiC,KAAY,EAAE,QAA0C;QACzF,MAAM,SAAS,GACb,uBAAA,IAAI,+BAAW,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,+BAAW,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1D,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CAAiC,KAAY,EAAE,QAA0C;QAC1F,MAAM,SAAS,GAAG,uBAAA,IAAI,+BAAW,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS;YAAE,OAAO,IAAI,CAAC;QAC5B,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAClE,IAAI,KAAK,IAAI,CAAC;YAAE,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAiC,KAAY,EAAE,QAA0C;QAC3F,MAAM,SAAS,GACb,uBAAA,IAAI,+BAAW,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,+BAAW,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1D,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;OAUG;IACH,OAAO,CACL,KAAY;QAMZ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,sBAAsB;YACtB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAc,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAES,KAAK,CAEb,KAAY,EACZ,GAAG,IAAwC;QAE3C,MAAM,SAAS,GAAkD,uBAAA,IAAI,+BAAW,CAAC,KAAK,CAAC,CAAC;QACxF,IAAI,SAAS,EAAE,CAAC;YACd,uBAAA,IAAI,+BAAW,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAQ,CAAC;YACjE,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAO,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAI,IAAY,CAAC,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAES,YAAY,CAAC,KAAuB;QAC5C,MAAM,SAAS,GAAG,uBAAA,IAAI,+BAAW,CAAC,KAAK,CAAC,CAAC;QACzC,OAAO,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3C,CAAC;CACF"}