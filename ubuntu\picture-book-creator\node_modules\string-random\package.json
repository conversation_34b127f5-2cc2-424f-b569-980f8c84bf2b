{"name": "string-random", "version": "0.1.3", "description": "Generate random string", "main": "index.js", "types": "index.d.ts", "directories": {"test": "test"}, "scripts": {"test": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/maichong/string-random.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/liangxingchen"}, "license": "MIT", "bugs": {"url": "https://github.com/maichong/string-random/issues"}, "homepage": "https://github.com/maichong/string-random#readme", "devDependencies": {"mocha": "^5.2.0"}}