/**
 * LiblibAI API代理服务器
 * 解决CORS问题，安全地处理API密钥
 */

import express from 'express';
import cors from 'cors';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// LiblibAI配置
const LIBLIB_CONFIG = {
  baseUrl: 'https://openapi.liblibai.cloud',
  accessKey: process.env.VITE_LIBLIB_ACCESS_KEY,
  secretKey: process.env.VITE_LIBLIB_SECRET_KEY,
  templateUuid: '5d7e67009b344550bc1aa6ccbfa1d7f4'
};



/**
 * 生成随机字符串（模拟Java的RandomStringUtils.randomAlphanumeric(10)）
 */
function generateRandomString(length = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成API签名（完全按照Java示例实现）
 */
function generateSignature(uri) {
  const timestamp = Date.now().toString();
  const signatureNonce = generateRandomString(10); // 使用10位随机字符串，与Java示例一致

  // 构建原文：uri + "&" + timestamp + "&" + signatureNonce（与Java示例完全一致）
  const content = uri + "&" + timestamp + "&" + signatureNonce;

  console.log('签名原文:', content);
  console.log('SecretKey:', LIBLIB_CONFIG.secretKey);

  // 使用HMAC-SHA1算法加密（与Java示例一致）
  const digest = crypto.createHmac('sha1', LIBLIB_CONFIG.secretKey)
    .update(content, 'utf8')
    .digest();

  // 使用Base64 URL安全编码（模拟Java的Base64.encodeBase64URLSafeString()）
  const signature = digest.toString('base64')
    .replace(/\+/g, '-')    // + 替换为 -
    .replace(/\//g, '_')    // / 替换为 _
    .replace(/=+$/, '');    // 去除末尾的 =

  console.log('生成的签名:', signature);

  return {
    signature,
    timestamp,
    signatureNonce
  };
}

/**
 * 构建请求头
 */
function buildHeaders(uri, isJsonRequest = true) {
  const { signature, timestamp, signatureNonce } = generateSignature(uri);
  
  const headers = {
    'AccessKey': LIBLIB_CONFIG.accessKey,
    'Signature': signature,
    'Timestamp': timestamp,
    'SignatureNonce': signatureNonce
  };
  
  if (isJsonRequest) {
    headers['Content-Type'] = 'application/json';
  }
  
  return headers;
}

// 根路径响应
app.get('/', (req, res) => {
  res.json({
    message: 'LiblibAI代理服务器运行正常',
    version: '1.0.0',
    endpoints: [
      'GET /api/liblib/config - 检查配置',
      'POST /api/liblib/text2img - 文生图',
      'POST /api/liblib/img2img - 图生图',
      'GET /api/liblib/query/:generateUuid - 查询结果'
    ]
  });
});

// 测试签名生成
app.get('/api/liblib/test-signature', (req, res) => {
  try {
    // 使用固定的测试数据
    const testUri = '/api/generate/webui/text2img/ultra';
    const testTimestamp = '1640995200000';
    const testNonce = 'abcd123456';
    const testContent = testUri + "&" + testTimestamp + "&" + testNonce;

    const digest = crypto.createHmac('sha1', LIBLIB_CONFIG.secretKey)
      .update(testContent, 'utf8')
      .digest();

    const signature = digest.toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    res.json({
      secretKey: LIBLIB_CONFIG.secretKey,
      uri: testUri,
      timestamp: testTimestamp,
      nonce: testNonce,
      content: testContent,
      rawBase64: digest.toString('base64'),
      urlSafeSignature: signature
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 检查配置
app.get('/api/liblib/config', (req, res) => {
  const isConfigured = !!(LIBLIB_CONFIG.accessKey && LIBLIB_CONFIG.secretKey);
  res.json({
    configured: isConfigured,
    message: isConfigured ? 'LiblibAI配置正常' : 'LiblibAI配置缺失，请检查环境变量'
  });
});

// 文生图API代理
app.post('/api/liblib/text2img', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ error: '缺少prompt参数' });
    }
    
    if (!LIBLIB_CONFIG.accessKey || !LIBLIB_CONFIG.secretKey) {
      return res.status(500).json({ error: 'LiblibAI API配置不完整' });
    }
    
    const uri = '/api/generate/webui/text2img/ultra';
    const url = LIBLIB_CONFIG.baseUrl + uri;
    const headers = buildHeaders(uri);
    
    const requestData = {
      templateUuid: LIBLIB_CONFIG.templateUuid,
      generateParams: {
        prompt: prompt.substring(0, 2000),
        ...options
      }
    };
    
    console.log('代理请求到LiblibAI:', url);
    console.log('请求数据:', requestData);
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });
    
    const result = await response.json();
    
    if (!response.ok) {
      console.error('LiblibAI API错误:', result);
      return res.status(response.status).json(result);
    }
    
    console.log('LiblibAI响应:', result);
    res.json(result);
    
  } catch (error) {
    console.error('代理服务器错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 图生图API代理
app.post('/api/liblib/img2img', async (req, res) => {
  try {
    const { prompt, imageUrl, options = {} } = req.body;
    
    if (!prompt || !imageUrl) {
      return res.status(400).json({ error: '缺少prompt或imageUrl参数' });
    }
    
    if (!LIBLIB_CONFIG.accessKey || !LIBLIB_CONFIG.secretKey) {
      return res.status(500).json({ error: 'LiblibAI API配置不完整' });
    }
    
    const uri = '/api/generate/webui/img2img/ultra';
    const url = LIBLIB_CONFIG.baseUrl + uri;
    const headers = buildHeaders(uri);
    
    const requestData = {
      templateUuid: LIBLIB_CONFIG.templateUuid,
      generateParams: {
        prompt: prompt.substring(0, 2000),
        init_images: [imageUrl],
        ...options
      }
    };
    
    console.log('代理图生图请求到LiblibAI:', url);
    console.log('请求数据:', requestData);
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });
    
    const result = await response.json();
    
    if (!response.ok) {
      console.error('LiblibAI图生图API错误:', result);
      return res.status(response.status).json(result);
    }
    
    console.log('LiblibAI图生图响应:', result);
    res.json(result);
    
  } catch (error) {
    console.error('图生图代理服务器错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 查询结果API代理
app.get('/api/liblib/query/:generateUuid', async (req, res) => {
  try {
    const { generateUuid } = req.params;
    
    if (!generateUuid) {
      return res.status(400).json({ error: '缺少generateUuid参数' });
    }
    
    if (!LIBLIB_CONFIG.accessKey || !LIBLIB_CONFIG.secretKey) {
      return res.status(500).json({ error: 'LiblibAI API配置不完整' });
    }
    
    const uri = '/api/generate/query';
    const url = `${LIBLIB_CONFIG.baseUrl}${uri}?generateUuid=${generateUuid}`;
    const headers = buildHeaders(uri, false);
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(url, {
      method: 'GET',
      headers: headers
    });
    
    const result = await response.json();
    
    if (!response.ok) {
      console.error('LiblibAI查询API错误:', result);
      return res.status(response.status).json(result);
    }
    
    res.json(result);
    
  } catch (error) {
    console.error('查询代理服务器错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`LiblibAI代理服务器运行在 http://localhost:${PORT}`);
  console.log('API端点:');
  console.log(`  - 配置检查: GET http://localhost:${PORT}/api/liblib/config`);
  console.log(`  - 文生图: POST http://localhost:${PORT}/api/liblib/text2img`);
  console.log(`  - 图生图: POST http://localhost:${PORT}/api/liblib/img2img`);
  console.log(`  - 查询结果: GET http://localhost:${PORT}/api/liblib/query/:generateUuid`);
});

export default app;
