import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { ChevronLeft, ChevronRight, Home, RotateCcw, BookOpen, Sparkles } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { generateLocalImage, loadImageWithTimeout } from '@/lib/imageUtils.js'

export default function PreviewPage() {
  const navigate = useNavigate()
  const [currentPage, setCurrentPage] = useState(0)
  const [bookData, setBookData] = useState(null)
  const [imageLoadStates, setImageLoadStates] = useState({})

  useEffect(() => {
    // 获取所有设置数据
    const characterData = JSON.parse(localStorage.getItem('characterData') || '{}')
    const storyData = JSON.parse(localStorage.getItem('storyData') || '{}')
    const contentData = JSON.parse(localStorage.getItem('contentData') || '{}')
    const generatedBook = JSON.parse(localStorage.getItem('generatedBook') || 'null')

    if (!characterData.name && !generatedBook) {
      console.warn('PreviewPage - 没有角色数据和生成的绘本，跳转到首页');
      navigate('/')
      return
    }

    // 调试信息
    console.log('PreviewPage - 检查生成的绘本数据:', generatedBook);
    console.log('PreviewPage - 角色数据:', characterData);
    console.log('PreviewPage - 故事数据:', storyData);
    console.log('PreviewPage - 内容数据:', contentData);

    // 如果有AI生成的内容，优先使用
    if (generatedBook && generatedBook.pages) {
      console.log('PreviewPage - 使用AI生成的内容，页数:', generatedBook.pages.length);
      const pages = [
        // 封面
        {
          type: 'cover',
          title: generatedBook.title,
          subtitle: contentData.isCustom ? contentData.customContent : contentData.randomTopic,
          image: '🌟'
        },
        // AI生成的内容页
        ...generatedBook.pages.map(page => ({
          type: 'content',
          pageNumber: page.pageNumber,
          title: page.title,
          content: page.content,
          sceneDescription: page.sceneDescription,
          imageUrl: page.imageUrl, // DALL-E 3生成的图像URL
          imagePrompt: page.imagePrompt, // 图像生成提示词
          fallbackEmoji: page.fallbackEmoji, // 备用emoji
          localImageUrl: generateLocalImage(page, characterData), // 本地生成的图像
          image: page.fallbackEmoji || ['🌈', '🦋', '🌸', '🌺', '🍀', '⭐', '🌙', '☀️', '🌻', '🎈'][page.pageNumber % 10]
        })),
        // 结尾页
        {
          type: 'ending',
          title: '故事结束',
          content: generatedBook.educationalMessage || `${characterData.name}通过这次经历学会了很多，变得更加勇敢和智慧。`,
          image: '🎉'
        }
      ]

      setBookData({
        character: characterData,
        story: storyData,
        content: contentData,
        pages: pages,
        isAIGenerated: true
      })
      return
    }

    // 如果没有AI生成的内容，使用默认示例内容
    const storyTypes = {
      'adventure': '冒险故事',
      'growth': '成长故事',
      'friendship': '友情故事',
      'life-skills': '生活技能'
    }

    const pages = []
    const totalPages = storyData.pages || 6
    
    // 封面
    pages.push({
      type: 'cover',
      title: `${characterData.name}的${storyTypes[storyData.type] || '奇妙'}之旅`,
      subtitle: contentData.isCustom ? contentData.customContent : contentData.randomTopic,
      image: '🌟'
    })

    // 内容页
    for (let i = 1; i < totalPages; i++) {
      pages.push({
        type: 'content',
        pageNumber: i,
        title: `第${i}页`,
        content: `这是${characterData.name}的故事第${i}页。在这一页中，${characterData.name}遇到了新的挑战和机会，学习了重要的人生道理...`,
        image: ['🌈', '🦋', '🌸', '🌺', '🍀', '⭐'][i % 6]
      })
    }

    // 结尾页
    pages.push({
      type: 'ending',
      title: '故事结束',
      content: `${characterData.name}通过这次经历学会了很多，变得更加勇敢和智慧。`,
      image: '🎉'
    })

    setBookData({
      character: characterData,
      story: storyData,
      content: contentData,
      pages: pages,
      isAIGenerated: false
    })
  }, [navigate])

  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1)
    }
  }

  const handleNextPage = () => {
    if (bookData && currentPage < bookData.pages.length - 1) {
      setCurrentPage(currentPage + 1)
    }
  }

  const handleBackHome = () => {
    navigate('/')
  }

  const handleRecreate = () => {
    localStorage.clear()
    navigate('/')
  }

  if (!bookData) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <BookOpen className="w-16 h-16 text-blue-500 mx-auto mb-4" />
          <p className="text-gray-500">加载中...</p>
        </div>
      </div>
    )
  }

  const currentPageData = bookData.pages[currentPage]



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      {/* 顶部导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <BookOpen className="w-6 h-6 text-blue-500 mr-3" />
            <h1 className="text-xl font-medium text-gray-800">绘本预览</h1>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">
              {currentPage + 1} / {bookData.pages.length}
            </span>
            <Button onClick={handleBackHome} variant="outline" size="sm">
              <Home className="w-4 h-4 mr-2" />
              返回首页
            </Button>
            <Button onClick={handleRecreate} variant="outline" size="sm">
              <RotateCcw className="w-4 h-4 mr-2" />
              重新创建
            </Button>
          </div>
        </div>
      </div>

      {/* 绘本内容 */}
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
          {/* 页面内容 */}
          <div className="aspect-[4/3] p-12 flex flex-col items-center justify-center text-center">
            {currentPageData.type === 'cover' && (
              <div className="space-y-6">
                <div className="text-8xl mb-6">{currentPageData.image}</div>
                <h1 className="text-4xl font-bold text-gray-800 mb-4">
                  {currentPageData.title}
                </h1>
                <p className="text-xl text-gray-600 max-w-md">
                  {currentPageData.subtitle}
                </p>
              </div>
            )}

            {currentPageData.type === 'content' && (
              <div className="space-y-8 max-w-2xl">
                {/* 智能图像显示 */}
                <ImageDisplay
                  pageData={currentPageData}
                  onImageLoad={(success) => {
                    setImageLoadStates(prev => ({
                      ...prev,
                      [currentPageData.pageNumber]: success
                    }));
                  }}
                />

                <h2 className="text-2xl font-semibold text-gray-800">
                  {currentPageData.title}
                </h2>
                <p className="text-lg text-gray-700 leading-relaxed">
                  {currentPageData.content}
                </p>


              </div>
            )}

            {currentPageData.type === 'ending' && (
              <div className="space-y-6">
                <div className="text-8xl mb-6">{currentPageData.image}</div>
                <h2 className="text-3xl font-bold text-gray-800 mb-4">
                  {currentPageData.title}
                </h2>
                <p className="text-xl text-gray-600 max-w-md">
                  {currentPageData.content}
                </p>
              </div>
            )}
          </div>

          {/* 翻页控制 */}
          <div className="bg-gray-50 px-8 py-6 flex items-center justify-between">
            <Button
              onClick={handlePrevPage}
              disabled={currentPage === 0}
              variant="outline"
              className="flex items-center"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              上一页
            </Button>

            <div className="flex space-x-2">
              {bookData.pages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentPage ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>

            <Button
              onClick={handleNextPage}
              disabled={currentPage === bookData.pages.length - 1}
              variant="outline"
              className="flex items-center"
            >
              下一页
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>

        {/* 绘本信息 */}
        <div className="mt-8 bg-white rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">绘本信息</h3>
            {bookData.isAIGenerated && (
              <div className="flex items-center space-x-2 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">
                <Sparkles className="w-3 h-3" />
                <span>AI生成</span>
              </div>
            )}
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
            <div>
              <span className="text-gray-500">主角姓名：</span>
              <span className="font-medium">{bookData.character.name}</span>
            </div>
            <div>
              <span className="text-gray-500">主角年龄：</span>
              <span className="font-medium">{bookData.character.age}岁</span>
            </div>
            <div>
              <span className="text-gray-500">角色身份：</span>
              <span className="font-medium">{bookData.character.identity === 'human' ? '人类' : '动物'}</span>
            </div>
            <div>
              <span className="text-gray-500">故事页数：</span>
              <span className="font-medium">{bookData.pages.length}页</span>
            </div>
          </div>

          {/* AI技术信息 */}
          {bookData.isAIGenerated && (
            <div className="border-t pt-4">
              <div className="text-sm text-gray-600 mb-2 font-medium">AI技术栈</div>
              <div className="flex flex-wrap gap-2">
                <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                  GPT-4 故事创作
                </div>
                <div className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
                  DALL-E 3 插画生成
                </div>
                <div className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                  个性化定制
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// 智能图像显示组件
function ImageDisplay({ pageData, onImageLoad }) {
  const [imageState, setImageState] = useState('loading') // loading, success, failed, fallback
  const [currentImageSrc, setCurrentImageSrc] = useState('')

  useEffect(() => {
    const loadImage = async () => {
      // 首先尝试DALL-E 3生成的图像
      if (pageData.imageUrl) {
        setImageState('loading')
        setCurrentImageSrc(pageData.imageUrl)

        // 对于DALL-E图像，我们直接尝试显示，让浏览器处理加载
        // 如果加载失败，onError会被触发
        setImageState('success')
        onImageLoad && onImageLoad(true)
        return
      }

      // 如果没有DALL-E图像，使用本地生成的图像
      if (pageData.localImageUrl) {
        setImageState('fallback')
        setCurrentImageSrc(pageData.localImageUrl)
        onImageLoad && onImageLoad(false)
        return
      }

      // 最后使用emoji备用方案
      setImageState('failed')
      onImageLoad && onImageLoad(false)
    }

    loadImage()
  }, [pageData.imageUrl, pageData.localImageUrl, pageData.pageNumber])

  return (
    <div className="mb-6">
      {imageState === 'loading' && (
        <div className="w-full max-w-md mx-auto h-64 bg-gray-100 rounded-2xl flex items-center justify-center">
          <div className="text-gray-500">加载中...</div>
        </div>
      )}

      {(imageState === 'success' || imageState === 'fallback') && (
        <div className="relative">
          <img
            src={currentImageSrc}
            alt={pageData.sceneDescription || pageData.title}
            className="w-full max-w-md mx-auto rounded-2xl shadow-lg block"
            onError={() => {
              // 如果DALL-E图像失败，尝试本地图像
              if (pageData.localImageUrl && currentImageSrc !== pageData.localImageUrl) {
                setCurrentImageSrc(pageData.localImageUrl)
                setImageState('fallback')
              } else {
                setImageState('failed')
              }
            }}
          />
          <div className="text-center mt-2 text-xs text-gray-500">
            {imageState === 'success' ? 'AI生成的插画' : '本地生成的插画'}
          </div>
        </div>
      )}

      {imageState === 'failed' && (
        <div className="mb-6">
          <div className="text-6xl text-center">{pageData.fallbackEmoji || pageData.image}</div>
          <div className="text-center mt-2 text-xs text-gray-500">
            默认插图
          </div>
        </div>
      )}


    </div>
  )
}

