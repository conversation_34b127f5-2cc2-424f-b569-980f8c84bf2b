{"hash": "145a5af8", "configHash": "ec48230a", "lockfileHash": "3b678a01", "browserHash": "2b0e5437", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "c88b871b", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "5492980e", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "c682f61a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "6c25fdfc", "needsInterop": true}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "99565a9f", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "2801ad30", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "3562159b", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "381b64d7", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "c11e59cf", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "92719048", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "b6a49dcc", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "d9cb8b35", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "898071d0", "needsInterop": false}, "crypto-js": {"src": "../../crypto-js/index.js", "file": "crypto-js.js", "fileHash": "d4a1f681", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "54abd837", "needsInterop": false}, "openai": {"src": "../../openai/index.mjs", "file": "openai.js", "fileHash": "b21d9168", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "e07c458e", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "14295c2d", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "edd9ea22", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-B3JFAQHC": {"file": "chunk-B3JFAQHC.js"}, "chunk-6ZMM2PAV": {"file": "chunk-6ZMM2PAV.js"}, "chunk-ZKGPRNYK": {"file": "chunk-ZKGPRNYK.js"}, "chunk-HOYPUAVF": {"file": "chunk-HOYPUAVF.js"}, "chunk-SLNJKUYB": {"file": "chunk-SLNJKUYB.js"}, "chunk-L52ERSML": {"file": "chunk-L52ERSML.js"}, "chunk-W4MM3P5G": {"file": "chunk-W4MM3P5G.js"}, "chunk-EJTTOCY5": {"file": "chunk-EJTTOCY5.js"}, "chunk-7CC4PDZ5": {"file": "chunk-7CC4PDZ5.js"}, "chunk-YHPANKLD": {"file": "chunk-YHPANKLD.js"}, "chunk-PR4QN5HX": {"file": "chunk-PR4QN5HX.js"}}}