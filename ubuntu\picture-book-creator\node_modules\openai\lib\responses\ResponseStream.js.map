{"version": 3, "file": "ResponseStream.js", "sourceRoot": "", "sources": ["../../src/lib/responses/ResponseStream.ts"], "names": [], "mappings": ";;;;;AASA,0CAA6D;AAE7D,mDAA8D;AAE9D,2DAA8E;AAkD9E,MAAa,cACX,SAAQ,yBAA2B;IAOnC,YAAY,MAAsC;QAChD,KAAK,EAAE,CAAC;;QALV,yCAAwC;QACxC,0DAA+C;QAC/C,gDAAoD;QAIlD,+BAAA,IAAI,0BAAW,MAAM,MAAA,CAAC;IACxB,CAAC;IAED,MAAM,CAAC,cAAc,CACnB,MAAc,EACd,MAA4B,EAC5B,OAAwB;QAExB,MAAM,MAAM,GAAG,IAAI,cAAc,CAAU,MAAuC,CAAC,CAAC;QACpF,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CACf,MAAM,CAAC,yBAAyB,CAAC,MAAM,EAAE,MAAM,EAAE;YAC/C,GAAG,OAAO;YACV,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,2BAA2B,EAAE,QAAQ,EAAE;SACxE,CAAC,CACH,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IA2ES,KAAK,CAAC,yBAAyB,CACvC,MAAc,EACd,MAA4B,EAC5B,OAAwB;QAExB,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,CAAC;QAC/B,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,MAAM,CAAC,OAAO;gBAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;QAClE,CAAC;QACD,+BAAA,IAAI,+DAAc,MAAlB,IAAI,CAAgB,CAAC;QAErB,IAAI,MAA+C,CAAC;QACpD,IAAI,cAAc,GAAkB,IAAI,CAAC;QACzC,IAAI,aAAa,IAAI,MAAM,EAAE,CAAC;YAC5B,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CACtC,MAAM,CAAC,WAAW,EAClB,EAAE,MAAM,EAAE,IAAI,EAAE,EAChB,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,CAC7D,CAAC;YACF,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CACpC,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAC3B,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAC/C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACjC,+BAAA,IAAI,2DAAU,MAAd,IAAI,EAAW,KAAK,EAAE,cAAc,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,yBAAiB,EAAE,CAAC;QAChC,CAAC;QACD,OAAO,+BAAA,IAAI,6DAAY,MAAhB,IAAI,CAAc,CAAC;IAC5B,CAAC;IAiED;QA7KE,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QACvB,+BAAA,IAAI,2CAA4B,SAAS,MAAA,CAAC;IAC5C,CAAC,+DAEwC,KAA0B,EAAE,cAA6B;QAChG,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QAEvB,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,KAAkD,EAAE,EAAE;YACrF,IAAI,cAAc,IAAI,IAAI,IAAI,KAAK,CAAC,eAAe,GAAG,cAAc,EAAE,CAAC;gBACrE,IAAI,CAAC,KAAK,CAAC,IAAW,EAAE,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,QAAQ,GAAG,+BAAA,IAAI,qEAAoB,MAAxB,IAAI,EAAqB,KAAK,CAAC,CAAC;QACjD,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAE1B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,4BAA4B,CAAC,CAAC,CAAC;gBAClC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,mBAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;gBACzE,CAAC;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC9B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;oBACpD,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,MAAM,IAAI,mBAAW,CAAC,4BAA4B,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;oBAC3E,CAAC;oBACD,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;wBACnC,MAAM,IAAI,mBAAW,CAAC,6CAA6C,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;oBACrF,CAAC;oBAED,SAAS,CAAC,4BAA4B,EAAE;wBACtC,GAAG,KAAK;wBACR,QAAQ,EAAE,OAAO,CAAC,IAAI;qBACvB,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;YACR,CAAC;YACD,KAAK,wCAAwC,CAAC,CAAC,CAAC;gBAC9C,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,mBAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;gBACzE,CAAC;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;oBACpC,SAAS,CAAC,wCAAwC,EAAE;wBAClD,GAAG,KAAK;wBACR,QAAQ,EAAE,MAAM,CAAC,SAAS;qBAC3B,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;YACR,CAAC;YACD;gBACE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC7B,MAAM;QACV,CAAC;IACH,CAAC;QAGC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,mBAAW,CAAC,yCAAyC,CAAC,CAAC;QACnE,CAAC;QACD,MAAM,QAAQ,GAAG,+BAAA,IAAI,+CAAyB,CAAC;QAC/C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,mBAAW,CAAC,0CAA0C,CAAC,CAAC;QACpE,CAAC;QACD,+BAAA,IAAI,2CAA4B,SAAS,MAAA,CAAC;QAC1C,MAAM,cAAc,GAAG,gBAAgB,CAAU,QAAQ,EAAE,+BAAA,IAAI,8BAAQ,CAAC,CAAC;QACzE,+BAAA,IAAI,iCAAkB,cAAc,MAAA,CAAC;QAErC,OAAO,cAAc,CAAC;IACxB,CAAC,mFAwCmB,KAA0B;QAC5C,IAAI,QAAQ,GAAG,+BAAA,IAAI,+CAAyB,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBACtC,MAAM,IAAI,mBAAW,CACnB,6EAA6E,KAAK,CAAC,IAAI,EAAE,CAC1F,CAAC;YACJ,CAAC;YACD,QAAQ,GAAG,+BAAA,IAAI,2CAA4B,KAAK,CAAC,QAAQ,MAAA,CAAC;YAC1D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,4BAA4B,CAAC,CAAC,CAAC;gBAClC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM;YACR,CAAC;YACD,KAAK,6BAA6B,CAAC,CAAC,CAAC;gBACnC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,mBAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;gBACzE,CAAC;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC9B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAClC,CAAC;gBACD,MAAM;YACR,CAAC;YACD,KAAK,4BAA4B,CAAC,CAAC,CAAC;gBAClC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,mBAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;gBACzE,CAAC;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC9B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;oBACpD,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,MAAM,IAAI,mBAAW,CAAC,4BAA4B,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;oBAC3E,CAAC;oBACD,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;wBACnC,MAAM,IAAI,mBAAW,CAAC,6CAA6C,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;oBACrF,CAAC;oBACD,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC;gBAC9B,CAAC;gBACD,MAAM;YACR,CAAC;YACD,KAAK,wCAAwC,CAAC,CAAC,CAAC;gBAC9C,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,mBAAW,CAAC,2BAA2B,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;gBACzE,CAAC;gBACD,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;oBACpC,MAAM,CAAC,SAAS,IAAI,KAAK,CAAC,KAAK,CAAC;gBAClC,CAAC;gBACD,MAAM;YACR,CAAC;YACD,KAAK,oBAAoB,CAAC,CAAC,CAAC;gBAC1B,+BAAA,IAAI,2CAA4B,KAAK,CAAC,QAAQ,MAAA,CAAC;gBAC/C,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC,EAEA,MAAM,CAAC,aAAa,EAAC;QACpB,MAAM,SAAS,GAA0B,EAAE,CAAC;QAC5C,MAAM,SAAS,GAGT,EAAE,CAAC;QACT,IAAI,IAAI,GAAG,KAAK,CAAC;QAEjB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACzB,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YACjC,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAClB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE,CAAC;gBAC/B,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC5B,CAAC;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE,CAAC;gBAC/B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE,CAAC;gBAC/B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,KAAK,IAAkD,EAAE;gBAC7D,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;oBACtB,IAAI,IAAI,EAAE,CAAC;wBACT,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;oBAC1C,CAAC;oBACD,OAAO,IAAI,OAAO,CAAkC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CACtE,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CACpC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBAChG,CAAC;gBACD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,EAAG,CAAC;gBACjC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YACvC,CAAC;YACD,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAC1C,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,QAAQ,GAAG,+BAAA,IAAI,qCAAe,CAAC;QACrC,IAAI,CAAC,QAAQ;YAAE,MAAM,IAAI,mBAAW,CAAC,iDAAiD,CAAC,CAAC;QACxF,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAjRD,wCAiRC;AAED,SAAS,gBAAgB,CACvB,QAAkB,EAClB,MAAsC;IAEtC,OAAO,IAAA,oCAAkB,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC"}