/**
 * 测试与Java示例完全一致的签名生成
 */

import crypto from 'crypto';

// 使用Java示例中的测试数据
const secretKey = "KppKsn7ezZxhi6lIDjbo7YyVYzanSu2d"; // Java示例中的密钥
const uri = "/api/generate/webui/text2img"; // Java示例中的URI（注意没有/ultra）
const timestamp = "1640995200000"; // 固定时间戳
const signatureNonce = "abcd123456"; // 固定10位随机字符串

console.log('=== 测试Java签名算法 ===');
console.log('SecretKey:', secretKey);
console.log('URI:', uri);
console.log('Timestamp:', timestamp);
console.log('SignatureNonce:', signatureNonce);

// 构建原文（与Java示例完全一致）
const content = uri + "&" + timestamp + "&" + signatureNonce;
console.log('原文:', content);

// 生成签名（模拟Java的实现）
const digest = crypto.createHmac('sha1', secretKey)
  .update(content, 'utf8')
  .digest();

const signature = digest.toString('base64')
  .replace(/\+/g, '-')    // + 替换为 -
  .replace(/\//g, '_')    // / 替换为 _
  .replace(/=+$/, '');    // 去除末尾的 =

console.log('生成的签名:', signature);

// 验证base64编码
console.log('原始base64:', digest.toString('base64'));
console.log('URL安全base64:', signature);

console.log('=== 测试完成 ===');
