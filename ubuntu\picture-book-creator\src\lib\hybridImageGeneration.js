/**
 * 混合图像生成服务
 * 结合DALL-E 3和LiblibAI，实现角色一致性的绘本插图生成
 * 
 * 工作流程：
 * 1. 使用DALL-E 3生成角色参考图（text2image）
 * 2. 使用LiblibAI基于参考图生成绘本插图（image2image）
 */

import OpenAI from 'openai';
import { generateAutismFriendlyPrompt, generateCharacterDescription } from './autismFriendlyPrompts.js';
import { generateTextToImageComplete, generateImageToImageComplete, checkLiblibConfig } from './liblibai.js';

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true
});

/**
 * 生成角色参考图像（使用DALL-E 3）
 * @param {Object} character - 角色信息
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<string>} 角色参考图像URL
 */
export async function generateCharacterReference(character, onProgress = null) {
  try {
    if (onProgress) onProgress('正在生成角色参考图...', 10);
    
    // 生成标准化角色描述
    const characterDesc = generateCharacterDescription(character);
    
    // 构建角色参考图的提示词
    const referencePrompt = [
      "A simple, clean, friendly cartoon illustration for a children's picture book.",
      `Character reference sheet showing ${characterDesc}.`,
      "The character is standing in a neutral pose, facing forward, with a gentle smile.",
      "The background is minimal and clean, using soft pastel colors.",
      "The style is flat, 2D, and consistent for character reference.",
      "No text in the image.",
      "Perfect for use as a character reference in children's book illustrations."
    ].join(" ");
    
    console.log('生成角色参考图的提示词:', referencePrompt);
    
    if (onProgress) onProgress('正在调用DALL-E 3...', 30);
    
    const imageResponse = await openai.images.generate({
      model: "dall-e-3",
      prompt: referencePrompt,
      size: "1024x1024",
      quality: "standard",
      n: 1,
    });
    
    const referenceImageUrl = imageResponse.data[0].url;
    
    if (onProgress) onProgress('角色参考图生成完成！', 100);
    
    console.log('角色参考图生成成功:', referenceImageUrl);
    return referenceImageUrl;
    
  } catch (error) {
    console.error('生成角色参考图失败:', error);
    if (onProgress) onProgress(`生成失败: ${error.message}`, 0);
    throw error;
  }
}

/**
 * 基于角色参考图生成绘本插图（使用LiblibAI）
 * @param {string} referenceImageUrl - 角色参考图URL
 * @param {Object} pageData - 页面数据
 * @param {Object} character - 角色信息
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<string>} 生成的插图URL
 */
export async function generateBookIllustration(referenceImageUrl, pageData, character, onProgress = null) {
  try {
    if (!checkLiblibConfig()) {
      throw new Error('LiblibAI API配置不完整，请检查环境变量');
    }
    
    if (onProgress) onProgress('正在生成绘本插图...', 10);
    
    // 使用自闭症友好的关键词生成提示词
    const illustrationPrompt = generateAutismFriendlyPrompt({
      character: character,
      sceneDescription: pageData.sceneDescription || 'A simple scene',
      emotion: 'calm',
      action: 'standing',
      environment: 'home'
    });
    
    console.log('生成绘本插图的提示词:', illustrationPrompt);
    
    if (onProgress) onProgress('正在调用LiblibAI图生图...', 30);
    
    // 使用LiblibAI的图生图功能
    const result = await generateImageToImageComplete(
      illustrationPrompt,
      referenceImageUrl,
      (message, progressValue) => {
        if (onProgress) {
          // 将LiblibAI的进度映射到30-100%
          const mappedProgress = 30 + (progressValue * 0.7);
          onProgress(message, mappedProgress);
        }
      }
    );
    
    if (result.status === 'success' && result.imageUrl) {
      if (onProgress) onProgress('绘本插图生成完成！', 100);
      console.log('绘本插图生成成功:', result.imageUrl);
      return result.imageUrl;
    } else {
      throw new Error('LiblibAI图生图失败，未获取到图像URL');
    }
    
  } catch (error) {
    console.error('生成绘本插图失败:', error);
    if (onProgress) onProgress(`生成失败: ${error.message}`, 0);
    throw error;
  }
}

/**
 * 完整的混合图像生成流程
 * @param {Object} character - 角色信息
 * @param {Array} pages - 页面数据数组
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Object>} 包含角色参考图和所有插图的结果
 */
export async function generateHybridPictureBook(character, pages, onProgress = null) {
  try {
    const results = {
      characterReference: null,
      illustrations: [],
      errors: []
    };
    
    // 第一步：生成角色参考图
    if (onProgress) onProgress('开始生成角色参考图...', 5);
    
    try {
      results.characterReference = await generateCharacterReference(
        character,
        (message, progress) => {
          if (onProgress) {
            // 角色参考图占总进度的20%
            const mappedProgress = 5 + (progress * 0.15);
            onProgress(`角色参考图: ${message}`, mappedProgress);
          }
        }
      );
    } catch (error) {
      results.errors.push(`角色参考图生成失败: ${error.message}`);
      console.warn('角色参考图生成失败，将跳过图生图功能');
    }
    
    // 第二步：为每页生成插图
    if (onProgress) onProgress('开始生成绘本插图...', 20);
    
    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];
      const pageProgress = 20 + ((i / pages.length) * 75); // 20%-95%
      
      try {
        if (onProgress) onProgress(`正在生成第${i + 1}页插图...`, pageProgress);
        
        let illustrationUrl;
        
        if (results.characterReference && checkLiblibConfig()) {
          // 使用混合方式：LiblibAI图生图
          illustrationUrl = await generateBookIllustration(
            results.characterReference,
            page,
            character,
            (message, progress) => {
              if (onProgress) {
                const mappedProgress = pageProgress + (progress * 0.75 / pages.length);
                onProgress(`第${i + 1}页: ${message}`, mappedProgress);
              }
            }
          );
        } else {
          // 降级到DALL-E 3文生图
          if (onProgress) onProgress(`第${i + 1}页: 使用DALL-E 3生成...`, pageProgress + 30);
          
          const fallbackPrompt = generateAutismFriendlyPrompt({
            character: character,
            sceneDescription: page.sceneDescription || 'A simple scene',
            emotion: 'calm',
            action: 'standing',
            environment: 'home'
          });
          
          const imageResponse = await openai.images.generate({
            model: "dall-e-3",
            prompt: fallbackPrompt,
            size: "1024x1024",
            quality: "standard",
            n: 1,
          });
          
          illustrationUrl = imageResponse.data[0].url;
        }
        
        results.illustrations.push({
          pageNumber: page.pageNumber,
          imageUrl: illustrationUrl,
          method: results.characterReference ? 'hybrid' : 'dalle3-only',
          success: true
        });
        
      } catch (error) {
        console.error(`第${i + 1}页插图生成失败:`, error);
        results.errors.push(`第${i + 1}页插图生成失败: ${error.message}`);
        
        results.illustrations.push({
          pageNumber: page.pageNumber,
          imageUrl: null,
          method: 'failed',
          success: false,
          error: error.message
        });
      }
    }
    
    if (onProgress) onProgress('混合图像生成完成！', 100);
    
    console.log('混合图像生成结果:', results);
    return results;
    
  } catch (error) {
    console.error('混合图像生成流程失败:', error);
    if (onProgress) onProgress(`生成失败: ${error.message}`, 0);
    throw error;
  }
}

/**
 * 检查混合图像生成的可用性
 * @returns {Object} 可用性状态
 */
export function checkHybridGenerationAvailability() {
  const openaiAvailable = !!(import.meta.env.VITE_OPENAI_API_KEY);
  const liblibAvailable = checkLiblibConfig();
  
  return {
    openai: openaiAvailable,
    liblib: liblibAvailable,
    hybrid: openaiAvailable && liblibAvailable,
    fallback: openaiAvailable // 至少可以使用DALL-E 3
  };
}

/**
 * 获取推荐的生成策略
 * @returns {string} 推荐策略
 */
export function getRecommendedStrategy() {
  const availability = checkHybridGenerationAvailability();
  
  if (availability.hybrid) {
    return 'hybrid'; // 混合模式：DALL-E 3 + LiblibAI
  } else if (availability.fallback) {
    return 'dalle3-only'; // 仅DALL-E 3
  } else {
    return 'none'; // 无可用服务
  }
}
